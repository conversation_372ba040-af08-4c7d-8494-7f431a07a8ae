<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.cmc</groupId>
    <artifactId>dquery-dex-service</artifactId>
    <version>1.0.40-SNAPSHOT</version>
  </parent>

  <groupId>com.cmc</groupId>
  <artifactId>dex-test-report</artifactId>
  <version>1.0.40-SNAPSHOT</version>

  <dependencies>
    <!--TODO: Make sure you have all other modules here-->
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-common</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-dao</artifactId>
      <version>${project.version}</version>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-job</artifactId>
      <version>${project.version}</version>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-service</artifactId>
      <version>${project.version}</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
        <version>${jacoco.version}</version>
        <executions>
          <execution>
            <id>aggregate-report</id>
            <phase>test</phase>
            <goals><goal>report-aggregate</goal></goals>
          </execution>
        </executions>
      </plugin>
    </plugins>
  </build>
</project>