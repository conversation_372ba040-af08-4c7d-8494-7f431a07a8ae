<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.cmc</groupId>
    <artifactId>dquery-dex-service</artifactId>
    <packaging>pom</packaging>
    <version>1.0.40-SNAPSHOT</version>
    <description>CMC sample project for backend projects</description>
    <modules>
        <module>dex-business</module>
        <module>dex-common</module>
        <module>dex-dao</module>
        <module>dex-model</module>
        <module>dex-service</module>
        <module>dex-job</module>
        <module>dex-test-report</module>
    </modules>

    <parent>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-bom-parent</artifactId>
        <!--TODO: make sure you find the latest version here:
         https://git.coinmarketcap.supply/cmc-backend/cmc-framework
         -->
        <version>2.1.0</version>
        <relativePath />
    </parent>

    <properties>
        <java.version>17</java.version>
        <maven-compiler-plugin.version>3.13.0</maven-compiler-plugin.version>
        <!--TODO: Change this path -->
        <aggregate.report.dir>dex-test-report/target/site/jacoco-aggregate/jacoco.xml</aggregate.report.dir>

        <sonar.coverage.jacoco.xmlReportPaths>./${aggregate.report.dir}</sonar.coverage.jacoco.xmlReportPaths>
        <spring.cloud.version>2023.0.5</spring.cloud.version>
        <jacoco.version>0.8.12</jacoco.version>
        <testng.version>6.8</testng.version>
        <mockito.version>5.11.0</mockito.version>
        <mockito-inline.version>5.2.0</mockito-inline.version>
        <org.mapstruct.version>1.5.5.Final</org.mapstruct.version>
        <mybatis.plus.version>3.5.8</mybatis.plus.version>
        <mybatis.version>3.5.16</mybatis.version>
        <dynamic-datasource.version>4.3.1</dynamic-datasource.version>
        <annotation-api.version>2.1.1</annotation-api.version>
        <docker.root.repo>346764516239.dkr.ecr.us-east-1.amazonaws.com</docker.root.repo>
        <dockerfile-maven.version>1.4.13</dockerfile-maven.version>
        <mysql.driver.version>8.0.15</mysql.driver.version>
        <docker.tag>latest</docker.tag>
        <docker.skip>false</docker.skip>
        <spring.data.elasticsearch.version>2.2.7.RELEASE</spring.data.elasticsearch.version>
        <lucene-core.version>7.7.3</lucene-core.version>
        <aws.sdk.version>2.16.1</aws.sdk.version>
        <javax-validation.version>2.0.1.Final</javax-validation.version>
        <kafka.version>3.1.2</kafka.version>
        <springdoc.version>2.3.0</springdoc.version>
        <lombok-mapstruct-binding.version>0.2.0</lombok-mapstruct-binding.version>
    </properties>

    <dependencyManagement>
        <dependencies>
            <dependency>
                <groupId>org.springframework.cloud</groupId>
                <artifactId>spring-cloud-dependencies</artifactId>
                <version>${spring.cloud.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>dex-business</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>dex-common</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>dex-dao</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>dex-model</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>dex-job</artifactId>
                <version>${project.version}</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>dex-service</artifactId>
                <version>${project.version}</version>
            </dependency>
            <!-- Dependencies for ut, probably we will move them into framework later-->
            <dependency>
                <groupId>org.testng</groupId>
                <artifactId>testng</artifactId>
                <version>${testng.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-core</artifactId>
                <version>${mockito.version}</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mockito</groupId>
                <artifactId>mockito-inline</artifactId>
                <version>${mockito-inline.version}</version>
                <scope>test</scope>
            </dependency>

            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-extension</artifactId>
                <version>${mybatis.plus.version}</version>
            </dependency>
            <dependency>
                <groupId>mysql</groupId>
                <artifactId>mysql-connector-java</artifactId>
                <version>${mysql.driver.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
                <version>${dynamic-datasource.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-starter-test</artifactId>
                <version>3.2.4</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.mybatis</groupId>
                <artifactId>mybatis</artifactId>
                <version>${mybatis.version}</version>
            </dependency>
            <dependency>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-test</artifactId>
                <version>2.5.12</version>
                <scope>test</scope>
            </dependency>
            <dependency>
                <groupId>org.springframework</groupId>
                <artifactId>spring-test</artifactId>
                <version>5.3.19</version>
            </dependency>
            <dependency>
                <groupId>com.cmc</groupId>
                <artifactId>cmc-framework-boot-starter-mongodb-reactive</artifactId>
                <version>2.1.0</version>
            </dependency>
            <!-- Java Bean Validation API -->
            <dependency>
                <groupId>jakarta.validation</groupId>
                <artifactId>jakarta.validation-api</artifactId>
                <version>3.0.2</version>
            </dependency>
            <!-- Hibernate Validator -->
            <dependency>
                <groupId>org.hibernate.validator</groupId>
                <artifactId>hibernate-validator</artifactId>
                <version>8.0.0.Final</version>
            </dependency>

            <dependency>
                <groupId>org.apache.lucene</groupId>
                <artifactId>lucene-core</artifactId>
                <version>${lucene-core.version}</version>
            </dependency>
            <dependency>
                <groupId>software.amazon.awssdk</groupId>
                <artifactId>bom</artifactId>
                <version>${aws.sdk.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
            <dependency>
                <groupId>org.mapstruct</groupId>
                <artifactId>mapstruct</artifactId>
                <version>${org.mapstruct.version}</version>
            </dependency>
            <dependency>
                <groupId>jakarta.annotation</groupId>
                <artifactId>jakarta.annotation-api</artifactId>
                <version>${annotation-api.version}</version>
            </dependency>
            <dependency>
                <groupId>com.baomidou</groupId>
                <artifactId>mybatis-plus-boot-starter</artifactId>
                <version>3.5.3.1</version>
            </dependency>
            <dependency>
                <groupId>org.springdoc</groupId>
                <artifactId>springdoc-openapi-starter-webflux-ui</artifactId>
                <version>${springdoc.version}</version>
            </dependency>
        </dependencies>
    </dependencyManagement>
    <!-- TODO: Change the url to the actual path -->
    <scm>
        <connection>scm:git:****************************:cmc-backend/dquery-dex-service.git</connection>
        <url>https://git.coinmarketcap.supply/cmc-backend/dquery-dex-service</url>
        <tag>1.0-SNAPSHOT</tag>
    </scm>
    <build>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${java.version}</source>
                    <target>${java.version}</target>
                    <release>${java.version}</release>
                    <compilerArgs>
                        <arg>--enable-preview</arg>
                    </compilerArgs>
                    <annotationProcessorPaths>
                        <!-- delete this if you don't use mapstruct -->
                        <path>
                            <groupId>org.mapstruct</groupId>
                            <artifactId>mapstruct-processor</artifactId>
                            <version>${org.mapstruct.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok</artifactId>
                            <version>${lombok.version}</version>
                        </path>
                        <path>
                            <groupId>org.projectlombok</groupId>
                            <artifactId>lombok-mapstruct-binding</artifactId>
                            <version>${lombok-mapstruct-binding.version}</version>
                        </path>
                    </annotationProcessorPaths>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <configuration>
                    <systemPropertyVariables>
                        <jacoco-agent.destfile>target/jacoco.exec</jacoco-agent.destfile>
                    </systemPropertyVariables>
                    <argLine>@{surefireArgLine} --enable-preview</argLine>
                    <excludedGroups>integration</excludedGroups>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.21.0</version>
                <executions>
                    <execution>
                        <phase>package</phase>
                        <inherited>false</inherited>
                        <goals>
                            <goal>aggregate-pmd-check</goal>
                        </goals>
                    </execution>
                </executions>
                <configuration>
                    <printFailingErrors>true</printFailingErrors>
                    <rulesets>
                        <!--                        <ruleset>rulesets/java/ali-comment.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-concurrent.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-constant.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-exception.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-flowcontrol.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-naming.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-oop.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-orm.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-other.xml</ruleset>-->
                        <!--                        <ruleset>rulesets/java/ali-set.xml</ruleset>-->
                        <ruleset>rulesets/java/cmc-swagger.xml</ruleset>
                    </rulesets>
                    <failurePriority>2</failurePriority>
                    <verbose>true</verbose>
                </configuration>
                <dependencies>
                    <dependency>
                        <groupId>com.alibaba.p3c</groupId>
                        <artifactId>p3c-pmd</artifactId>
                        <version>2.1.1</version>
                    </dependency>
                    <dependency>
                        <groupId>com.cmc</groupId>
                        <artifactId>coding-convention-pmd</artifactId>
                        <version>[1.0.0,)</version>
                    </dependency>
                </dependencies>
            </plugin>
        </plugins>

        <pluginManagement>
            <plugins>
                <plugin>
                    <groupId>com.spotify</groupId>
                    <artifactId>dockerfile-maven-plugin</artifactId>
                    <version>${dockerfile-maven.version}</version>
                    <configuration>
                        <skip>${docker.skip}</skip>
                        <skipDockerInfo>true</skipDockerInfo>
                        <repository>${docker.repo}</repository>
                        <tag>${docker.tag}</tag>
                        <buildArgs>
                            <JAR_FILE>${project.build.finalName}.jar</JAR_FILE>
                        </buildArgs>
                    </configuration>
                    <executions>
                        <execution>
                            <id>default</id>
                            <phase>package</phase>
                            <goals>
                                <goal>build</goal>
                                <goal>push</goal>
                            </goals>
                        </execution>
                    </executions>
                </plugin>
                <plugin>
                    <groupId>org.jacoco</groupId>
                    <artifactId>jacoco-maven-plugin</artifactId>
                    <version>${jacoco.version}</version>
                    <executions>
                        <execution>
                            <id>default-prepare-agent</id>
                            <goals><goal>prepare-agent</goal></goals>
                        </execution>
                    </executions>
                    <configuration>
                        <propertyName>surefireArgLine</propertyName>
                    </configuration>
                </plugin>
            </plugins>
        </pluginManagement>
    </build>

    <reporting>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-pmd-plugin</artifactId>
                <version>3.21.0</version>
                <reportSets>
                    <reportSet>
                        <id>aggregate</id>
                        <inherited>false</inherited>
                        <reports>
                            <report>aggregate-pmd-no-fork</report>
                        </reports>
                    </reportSet>
                </reportSets>
            </plugin>
        </plugins>
    </reporting>

    <distributionManagement>
        <repository>
            <id>nexus-releases</id>
            <url>https://nexus.coinmarketcap.supply/repository/maven-releases</url>
        </repository>
        <snapshotRepository>
            <id>nexus-snapshots</id>
            <url>https://nexus.coinmarketcap.supply/repository/maven-snapshots</url>
        </snapshotRepository>
    </distributionManagement>
</project>
