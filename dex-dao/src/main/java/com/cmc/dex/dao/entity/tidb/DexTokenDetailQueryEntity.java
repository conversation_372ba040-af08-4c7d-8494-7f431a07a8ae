package com.cmc.dex.dao.entity.tidb;

import java.math.BigDecimal;
import java.util.Date;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * DexTokenDetailQueryEntity
 * <AUTHOR> ricky.x
 * @date: 2025/5/7 22:40
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DexTokenDetailQueryEntity {
    private Integer platform;
    private Integer limit;
    private String lastAddress;
    private BigDecimal gteLiquidity;
    private BigDecimal gteMc;
    private BigDecimal gteVolume5m;
    private Long gteTxs5m;
    private BigDecimal gteVolume1h;
    private Long gteTxs1h;
    private BigDecimal gteVolume4h;
    private Long gteTxs4h;
    private BigDecimal gteVolume24h;
    private Long gteTxs24h;

    private Date gteLaunchAt;
}