package com.cmc.dex.dao.repository.es;

import com.cmc.dex.dao.entity.es.TokenEsEntity;
import java.util.List;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.annotations.Query;
import org.springframework.data.elasticsearch.repository.ReactiveElasticsearchRepository;
import org.springframework.stereotype.Repository;
import reactor.core.publisher.Flux;

/**
 * Reactive ElasticSearch repository for token sa operations.
 *
 * <AUTHOR>
 * @since 2025/7/4
 */
@Repository
public interface TokenEsRepository extends ReactiveElasticsearchRepository<TokenEsEntity, String> {

    /**
     * Search symbol with Spring's native sorting and pagination
     * <p>
     * Search strategy:
     * <pre>
     * - Exact match (boost=3.0)
     * - Prefix match (boost=2.0)
     * - Tolerant match (boost=1.0):
     *   - For input length <= 8: use symbol_tolerant_search_short_analyzer
     *   - For input length > 8: use symbol_tolerant_search_long_analyzer
     * </pre>
     * <p>
     * Usage example:
     * <pre>
     * PageRequest.of(0, 100, Sort.by(Sort.Direction.DESC, "volumeUsd24h"))
     * </pre>
     *
     * @param symbol search keyword
     * @param platformId optional platform ID filter
     * @param blackPlatformIds optional list of platform IDs to exclude
     * @param pageable pagination and sorting info
     * @return flux of matching tokens
     */
    @Query("""
        {
          "bool": {
            "must": [
              {
                "bool": {
                  "should": [
                    {
                      "term": {
                        "symbol": {
                          "value": "?0",
                          "boost": 3.0
                        }
                      }
                    },
                    {
                      "match": {
                        "symbol.prefix": {
                          "query": "?0",
                          "boost": 2.0
                        }
                      }
                    },
                    {
                      "bool": {
                        "must": [
                          {
                            "match": {
                              "symbol.tolerant": {
                                "query": "?0",
                                "analyzer": "#{T(java.lang.String).valueOf(#root[0]).length() <= 8 ? 'symbol_tolerant_search_short_analyzer' : 'symbol_tolerant_search_long_analyzer'}",
                                "boost": 1.0
                              }
                            }
                          }
                        ]
                      }
                    }
                  ],
                  "minimum_should_match": 1
                }
              }
<<<<<<< HEAD
              #{#root[1] != null ? ', { "term": { "platformId": ' + #root[1] + ' } }' : ''}
=======
              #{#root[1] != null ? ', { "term": { "platformId": "' + #root[1] + '" } }' : ''}
>>>>>>> a2fe91383 (feat: add token es repository)
              #{#root[2] != null && !#root[2].isEmpty() ? ', { "bool": { "must_not": [ { "terms": { "platformId": ' + T(com.fasterxml.jackson.databind.ObjectMapper).builder().build().writeValueAsString(#root[2]) + ' } } ] } }' : ''}
            ]
          }
        }
        """)
<<<<<<< HEAD
    Flux<TokenEsEntity> findBySymbol(String symbol, Integer platformId, List<Integer> blackPlatformIds, Pageable pageable);
=======
    Flux<TokenEsEntity> findBySymbol(String symbol, String platformId, List<String> blackPlatformIds, Pageable pageable);
>>>>>>> a2fe91383 (feat: add token es repository)

    /**
     * Search address with platform filters
     *
     * @param address search keyword
     * @param platformId optional platform ID filter
     * @param blackPlatformIds optional list of platform IDs to exclude
     * @param pageable pagination
     */
    @Query("""
        {
          "bool": {
            "must": [
              {
                "term": {
                  "address": "?0"
                }
              }
<<<<<<< HEAD
              #{#root[1] != null ? ', { "term": { "platformId": ' + #root[1] + ' } }' : ''}
=======
              #{#root[1] != null ? ', { "term": { "platformId": "' + #root[1] + '" } }' : ''}
>>>>>>> a2fe91383 (feat: add token es repository)
              #{#root[2] != null && !#root[2].isEmpty() ? ', { "bool": { "must_not": [ { "terms": { "platformId": ' + T(com.fasterxml.jackson.databind.ObjectMapper).builder().build().writeValueAsString(#root[2]) + ' } } ] } }' : ''}
            ]
          }
        }
        """)
<<<<<<< HEAD
    Flux<TokenEsEntity> findByAddress(String address, Integer platformId, List<Integer> blackPlatformIds, Pageable pageable);
=======
    Flux<TokenEsEntity> findByAddress(String address, String platformId, List<String> blackPlatformIds, Pageable pageable);
>>>>>>> a2fe91383 (feat: add token es repository)
}