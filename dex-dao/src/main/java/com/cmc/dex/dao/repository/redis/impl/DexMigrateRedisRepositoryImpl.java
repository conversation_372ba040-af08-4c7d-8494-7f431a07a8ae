package com.cmc.dex.dao.repository.redis.impl;

import com.cmc.dex.common.constants.RedisConstants;
import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.dex.dao.entity.tidb.DexMigrateDetailEntity;
import com.cmc.dex.dao.repository.redis.DexMigrateRedisRepository;
import com.cmc.framework.redis.ReactiveRedisServiceImpl;
import com.cmc.framework.redis.annotation.RedisAutowiredBean;
import java.time.Duration;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Repository;
import cn.hutool.core.util.RandomUtil;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;

/**
 * <AUTHOR>
 */
@Repository
public class DexMigrateRedisRepositoryImpl extends ReactiveRedisServiceImpl implements DexMigrateRedisRepository {

    @Value("${com.cmc.dex.dev-migrate.expire.second:600}")
    private int cacheExpireTime;

    public DexMigrateRedisRepositoryImpl(@Autowired @Qualifier("backend.redis.dexer") RedisAutowiredBean autowiredBean) {
        super(autowiredBean.getProperties(), autowiredBean.getReactiveRedisTemplate());
    }


    @Override
    public Mono<String> getMigrateCount(Integer platformId, String userAddress) {
        return reactiveRedisTemplate.opsForValue().get(buildKey(platformId, userAddress))
                .publishOn(DexSchedulers.business());
    }

    @Override
    public Mono<Long> removeCache(List<DexMigrateDetailEntity> migrateDetailEntities) {
        List<String> keys = migrateDetailEntities.stream().map(e -> buildKey(e.getPlatform(),e.getUserAddress())).toList();
        return reactiveRedisTemplate.delete(Flux.fromIterable(keys));
    }

    @Override
    public Mono<Boolean> setMigrateCount(Integer platformId, String userAddress, Integer count) {
        long expireTime = RandomUtil.randomLong(cacheExpireTime / 2, cacheExpireTime);
        return reactiveRedisTemplate.opsForValue().set(buildKey(platformId, userAddress),
                String.valueOf(count), Duration.ofSeconds(expireTime))
                .publishOn(DexSchedulers.business());
    }

    private String buildKey(Integer platformId, String userAddress) {
        String key = platformId.toString().concat("_").concat(userAddress);
        return String.format(RedisConstants.DEV_MIGRATE_COUNT,key);
    }
}
