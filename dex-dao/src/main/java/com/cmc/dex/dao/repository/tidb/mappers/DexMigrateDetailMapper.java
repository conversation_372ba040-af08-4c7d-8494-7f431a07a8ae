package com.cmc.dex.dao.repository.tidb.mappers;

import static com.cmc.dex.common.constants.DataSourceConstants.DEX_API;

import com.cmc.dex.dao.entity.tidb.DexMigrateDetailEntity;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import java.util.List;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

/**
 * DexMigrateDetailMapper
 * <AUTHOR> ricky.x
 * @date: 2025/6/20 11:04
 */
@Mapper
@DS(DEX_API)
public interface DexMigrateDetailMapper extends BaseMapper<DexMigrateDetailEntity> {

    @Insert("""
            <script>
            INSERT INTO dex_migrate_detail (
                platform, address, user_address, publish_at,
                launched_at, pool_source,
                db_create_time,db_modify_time
            ) VALUES
            <foreach collection="entities" item="entity" separator=",">
                (
                    #{entity.platform}, #{entity.address}, #{entity.userAddress},#{entity.publishAt},
                    #{entity.launchedAt}, #{entity.poolSource},
                    NOW(), NOW()
                )
            </foreach>
            ON DUPLICATE KEY UPDATE
                user_address = IF(user_address IS NULL, VALUES(user_address), user_address),
                publish_at = IF(publish_at IS NULL, VALUES(publish_at), publish_at),
                launched_at = IF(launched_at IS NULL, VALUES(launched_at), launched_at),
                pool_source = IF(pool_source IS NULL, VALUES(pool_source), pool_source),
                db_modify_time = NOW()
            </script>
            """)
    int batchInsert(@Param("entities") List<DexMigrateDetailEntity> list);


    @Select("""
            SELECT count(*) FROM dex_migrate_detail
            WHERE platform = #{platform} AND user_address = #{userAddress}
            """)
    int countByPlatformAndUserCa(@Param("platform") Integer platform, @Param("userAddress") String userAddress);


    @Select("""
            <script>
            SELECT * FROM dex_migrate_detail
            WHERE platform = #{platform} AND user_address is null
            ORDER BY launched_at desc
            LIMIT #{limit}
            </script>
            """)
    List<DexMigrateDetailEntity> queryMissUserCa(@Param("platform") Integer platform, @Param("limit") int limit);
}