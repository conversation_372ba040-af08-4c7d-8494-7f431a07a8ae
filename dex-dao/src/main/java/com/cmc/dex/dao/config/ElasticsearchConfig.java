package com.cmc.dex.dao.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.elasticsearch.client.ClientConfiguration;
import org.springframework.data.elasticsearch.client.elc.ElasticsearchConfiguration;
import org.springframework.data.elasticsearch.repository.config.EnableReactiveElasticsearchRepositories;

/**
 * ElasticSearch configuration
 *
 * <AUTHOR>
 * @since 2025/7/7
 */
@Configuration
@EnableReactiveElasticsearchRepositories(basePackages = "com.cmc.dex.dao.repository.es")
public class ElasticsearchConfig extends ElasticsearchConfiguration {

    @Value("${spring.elasticsearch.host:localhost}")
    private String elasticsearchHost;

    @Value("${spring.elasticsearch.port:9200}")
    private int elasticsearchPort;

    @Value("${spring.elasticsearch.username:}")
    private String username;

    @Value("${spring.elasticsearch.password:}")
    private String password;

    @Value("${spring.elasticsearch.connection-timeout:10s}")
    private String connectionTimeout;

    @Value("${spring.elasticsearch.socket-timeout:30s}")
    private String socketTimeout;

    @Override
    public ClientConfiguration clientConfiguration() {
        // Build the connection URL correctly
        String connectionUrl = String.format("%s:%d", elasticsearchHost, elasticsearchPort);
        
        // For Spring Data Elasticsearch 5.x, use the new API in a single chain
        var builder = ClientConfiguration.builder()
            .connectedTo(connectionUrl)
            .withConnectTimeout(java.time.Duration.parse("PT" + connectionTimeout.toUpperCase()))
            .withSocketTimeout(java.time.Duration.parse("PT" + socketTimeout.toUpperCase()));

        // Add authentication if provided
        if (!username.isEmpty() && !password.isEmpty()) {
            return builder.withBasicAuth(username, password).build();
        }

        return builder.build();
    }
}