package com.cmc.dex.dao.repository.tidb.repository;

import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.dex.dao.entity.tidb.DexMigrateDetailEntity;
import com.cmc.dex.dao.repository.tidb.mappers.DexMigrateDetailMapper;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.StringUtils;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import java.util.Date;
import java.util.List;
import org.apache.ibatis.session.ExecutorType;
import org.apache.ibatis.session.SqlSession;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * batchInsertOrUpdateMetaData
 * <AUTHOR> ricky.x
 * @date: 2025/5/14 16:32
 */
@Service
@Slf4j
public class DexMigrateDetailRepository {

    @Autowired
    private DexMigrateDetailMapper dexMigrateDetailMapper;

    @Autowired
    private SqlSessionFactory sqlSessionFactory;

    public Mono<Integer> batchInsert(List<DexMigrateDetailEntity> list) {
        return Mono.fromCallable(() -> dexMigrateDetailMapper.batchInsert(list))
                .publishOn(DexSchedulers.business());
    }

    public Mono<Boolean> batchUpdateUserAddress(List<DexMigrateDetailEntity> list){
        if (CollectionUtils.isEmpty(list)) {
            return Mono.just(Boolean.TRUE);
        }
        return Mono.fromCallable(() -> {
            try (SqlSession sqlSession = sqlSessionFactory.openSession(ExecutorType.BATCH)) {
                for (DexMigrateDetailEntity entity : list) {
                    DexMigrateDetailEntity update = new DexMigrateDetailEntity();
                    if (StringUtils.isNotBlank(entity.getUserAddress())) {
                        update.setUserAddress(entity.getUserAddress());
                    }
                    update.setDbModifyTime(new Date());
                    UpdateWrapper<DexMigrateDetailEntity> updateWrapper = new UpdateWrapper<>();
                    updateWrapper.eq("platform", entity.getPlatform()).eq("address", entity.getAddress());
                    dexMigrateDetailMapper.update(update, updateWrapper);
                }
                sqlSession.commit();
            }
            return Boolean.TRUE;
        }).thenReturn(Boolean.TRUE).publishOn(DexSchedulers.business());
    }


    public Mono<Integer> countByPlatformAndUserCa(Integer platform, String userAddress) {
        return Mono.fromCallable(() -> dexMigrateDetailMapper.countByPlatformAndUserCa(platform, userAddress))
                .publishOn(DexSchedulers.business());
    }

    public Mono<List<DexMigrateDetailEntity>> queryMissUserCa(Integer platform,int limit) {
        return Mono.fromCallable(() -> dexMigrateDetailMapper.queryMissUserCa(platform, limit))
                .publishOn(DexSchedulers.business());
    }
}
