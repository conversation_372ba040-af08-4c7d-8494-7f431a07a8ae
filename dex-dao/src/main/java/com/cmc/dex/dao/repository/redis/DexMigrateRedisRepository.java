package com.cmc.dex.dao.repository.redis;

import com.cmc.dex.dao.entity.tidb.DexMigrateDetailEntity;
import com.cmc.framework.redis.ReactiveRedisService;
import java.util.List;
import reactor.core.publisher.Mono;

/**
 * DevMigrateRepository
 * <AUTHOR> ricky.x
 * @date: 2025/6/23 14:41
 */
public interface DexMigrateRedisRepository extends ReactiveRedisService {

    Mono<String> getMigrateCount(Integer platformId, String userAddress);


    Mono<Long> removeCache(List<DexMigrateDetailEntity> migrateDetailEntities);


    Mono<Boolean> setMigrateCount(Integer platformId, String userAddress, Integer count);

}
