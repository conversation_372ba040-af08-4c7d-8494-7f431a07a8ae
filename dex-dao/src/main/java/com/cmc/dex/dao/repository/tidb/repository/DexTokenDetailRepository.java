package com.cmc.dex.dao.repository.tidb.repository;

import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailQueryEntity;
import com.cmc.dex.dao.repository.tidb.mappers.DexTokenDetailMapper;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import java.util.Set;
import org.springframework.stereotype.Component;
import reactor.core.publisher.Mono;


/**
 * DexPoolDetailRepositoryImpl
 *
 * <AUTHOR>
 * @since 2025/5/10
 */
@Component
@DS("dexapi")
public class DexTokenDetailRepository {

    private final DexTokenDetailMapper dexTokenDetailMapper;

    public DexTokenDetailRepository(DexTokenDetailMapper dexTokenDetailMapper) {
        this.dexTokenDetailMapper = dexTokenDetailMapper;
    }

    public Mono<DexTokenDetailEntity> getTokenDetailEntity(String tokenAddress, Integer platform) {
        return Mono.fromCallable(() -> {
            LambdaQueryWrapper<DexTokenDetailEntity> queryWrapper = Wrappers.<DexTokenDetailEntity>lambdaQuery()
                .eq(DexTokenDetailEntity::getAddress, tokenAddress)
                .eq(DexTokenDetailEntity::getPlatform, platform);

            return dexTokenDetailMapper.selectOne(queryWrapper);
        });
    }

    public List<DexTokenDetailEntity> queryDexGainersWrapper(BigDecimal volumeUsd24h,
                                                             BigDecimal priceChange24h,
                                                             String lastAddress,
                                                             Integer limit,
                                                             Boolean forceIndex,
                                                             String tableIndex) {

        LambdaQueryWrapper<DexTokenDetailEntity> wrapper = Wrappers.lambdaQuery();

        // ========== SELECT 显式字段 ==========
        wrapper.select(
                DexTokenDetailEntity::getPlatform,
                DexTokenDetailEntity::getAddress,
                DexTokenDetailEntity::getPublishAt,
                DexTokenDetailEntity::getMarketcap,
                DexTokenDetailEntity::getLiquidity,
                DexTokenDetailEntity::getAuditPass,
                DexTokenDetailEntity::getVolumeUsd5m,
                DexTokenDetailEntity::getTxs5m,
                DexTokenDetailEntity::getBuyTxs5m,
                DexTokenDetailEntity::getSellTxs5m,
                DexTokenDetailEntity::getPriceChange5m,
                DexTokenDetailEntity::getVolumeUsd1h,
                DexTokenDetailEntity::getTxs1h,
                DexTokenDetailEntity::getBuyTxs1h,
                DexTokenDetailEntity::getSellTxs1h,
                DexTokenDetailEntity::getPriceChange1h,
                DexTokenDetailEntity::getVolumeUsd4h,
                DexTokenDetailEntity::getBuyVolume4h,
                DexTokenDetailEntity::getSellVolume4h,
                DexTokenDetailEntity::getTxs4h,
                DexTokenDetailEntity::getBuyTxs4h,
                DexTokenDetailEntity::getSellTxs4h,
                DexTokenDetailEntity::getPriceChange4h,
                DexTokenDetailEntity::getVolumeUsd24h,
                DexTokenDetailEntity::getTxs24h,
                DexTokenDetailEntity::getBuyTxs24h,
                DexTokenDetailEntity::getSellTxs24h,
                DexTokenDetailEntity::getPriceChange24h,
                DexTokenDetailEntity::getCreatedAt,
                DexTokenDetailEntity::getUpdatedAt,
                DexTokenDetailEntity::getTwitter,
                DexTokenDetailEntity::getWebsite,
                DexTokenDetailEntity::getTelegram
        );

        // ========== WHERE 条件 ==========
        wrapper.gt(DexTokenDetailEntity::getVolumeUsd24h, volumeUsd24h)
                .gt(DexTokenDetailEntity::getPriceChange24h, priceChange24h);

        if (lastAddress != null) {
            wrapper.gt(DexTokenDetailEntity::getAddress, lastAddress);
        }

        // ========== ORDER BY ==========
        wrapper.orderByAsc(DexTokenDetailEntity::getAddress);

        return dexTokenDetailMapper.selectList(wrapper);
    }


    public Mono<List<DexTokenDetailEntity>> batchGetTokenDetailEntities(Integer platform, List<String> addresses) {
        return Mono.fromCallable(() -> {
            LambdaQueryWrapper<DexTokenDetailEntity> queryWrapper = Wrappers.<DexTokenDetailEntity>lambdaQuery()
                    .eq(DexTokenDetailEntity::getPlatform, platform)
                    .in(DexTokenDetailEntity::getAddress, addresses);
            return dexTokenDetailMapper.selectList(queryWrapper);
        });
    }

    public Mono<List<DexTokenDetailEntity>> scanTokenPage(Integer platformId, String addresses, int limit) {
        return Mono.fromCallable(() -> {
            return dexTokenDetailMapper.scanTokenPage(platformId, addresses, limit);
        });
    }

    public Long queryAllCount() {
        return dexTokenDetailMapper.queryAllCount();
    }

    public Long queryTokenCount(Integer platformId) {
        return dexTokenDetailMapper.queryTokenCount(platformId);
    }

    public Mono<List<DexTokenDetailEntity>> queryByPlatformAndAddress(Integer platformId, Set<String> addresses) {
        return Mono.fromCallable(() -> {
            LambdaQueryWrapper<DexTokenDetailEntity> queryWrapper = Wrappers.<DexTokenDetailEntity>lambdaQuery()
                    .eq(DexTokenDetailEntity::getPlatform, platformId)
                    .in(DexTokenDetailEntity::getAddress, addresses);
            return dexTokenDetailMapper.selectList(queryWrapper);
        });
    }

    public Mono<List<DexTokenDetailEntity>> queryBetweenCreateTimeAndPublishAtIsNUll(Date startTime, Date endTime, Integer limit) {
        return Mono.fromCallable(() -> {
            LambdaQueryWrapper<DexTokenDetailEntity> queryWrapper = Wrappers.<DexTokenDetailEntity>lambdaQuery();

            if (startTime != null) {
                queryWrapper.gt(DexTokenDetailEntity::getCreatedAt, startTime); // >= startTime
            }
            if (endTime != null) {
                queryWrapper.le(DexTokenDetailEntity::getCreatedAt, endTime);   // <= endTime
            }
            queryWrapper.isNull(DexTokenDetailEntity::getPublishAt); // publishAt is NULL

            queryWrapper.orderByDesc(DexTokenDetailEntity::getCreatedAt);
            queryWrapper.last("LIMIT " + limit);
            return dexTokenDetailMapper.selectList(queryWrapper);
        }).publishOn(DexSchedulers.business());
    }

    public Mono<List<DexTokenDetailEntity>> queryLaunchByLaunchAt(Integer platform, String lastAddress, Integer limit) {
        DexTokenDetailQueryEntity queryEntity = new DexTokenDetailQueryEntity();
        queryEntity.setPlatform(platform);
        if (StringUtils.isBlank(lastAddress)) {
            queryEntity.setLastAddress("");
        } else {
            queryEntity.setLastAddress(lastAddress);
        }
        queryEntity.setLimit(limit);
        return Mono.fromCallable(() -> dexTokenDetailMapper.queryDetailForMigrateSync(queryEntity))
                .publishOn(DexSchedulers.business());
    }

    public Mono<List<DexTokenDetailEntity>> batchGetPkField(List<DexTokenDetailEntity> keys) {
        return Mono.fromCallable(() -> dexTokenDetailMapper.batchGetPkField(keys))
                .publishOn(DexSchedulers.business());
    }

    public Mono<List<DexTokenDetailEntity>> batchGetUserToken(Integer platform,String userAddress, Date gtePublishAt, Integer limit) {
        LambdaQueryWrapper<DexTokenDetailEntity> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.select(DexTokenDetailEntity::getAddress);
        queryWrapper.eq(DexTokenDetailEntity::getPlatform, platform);
        queryWrapper.eq(DexTokenDetailEntity::getUserAddress, userAddress);
        queryWrapper.gt(DexTokenDetailEntity::getPublishAt, gtePublishAt);
        queryWrapper.orderBy(true, false, DexTokenDetailEntity::getPublishAt);
        if (limit != null) {
            queryWrapper.last("LIMIT " + limit);
        }
        return Mono.fromSupplier(() -> dexTokenDetailMapper.selectList(queryWrapper))
                .publishOn(DexSchedulers.business());
    }

}
