package com.cmc.dex.dao.repository.tidb.mappers;

import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailQueryEntity;
import com.cmc.dex.model.token.TokenDetailExtendDTO;
import static com.cmc.dex.common.constants.DataSourceConstants.DEX_API;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;
import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
@DS(DEX_API)
public interface DexTokenDetailMapper extends BaseMapper<DexTokenDetailEntity> {
    @Insert("""
                    <script>
                       INSERT INTO dex_token_detail (
                                 platform, address, address_ci, name, symbol, symbol_ci, decimals, total_supply, burn_supply,
                                 circulating_supply, holders,
                                 account,user_address,
                                 publish_at, launched_at, trade_at, creator,
                                 owner, twitter, website, logo_url, image_url,
                                 telegram,
                                 pool_source,
                                 fdv, marketcap, liquidity,
                                 liquidity_usd, liquidity_score, confidence_score, price, price_usd, price_high,
                                 price_low, audit_pass, liquidity_abnormal, bonding_curve_ratio, total_txs,
                                 volume_1m, buy_volume_1m, buy_volume_usd_1m, sell_volume_usd_1m, sell_volume_1m, volume_usd_1m, buy_txs_1m,
                                 sell_txs_1m, price_change_1m, traders_1m, buy_traders_1m, sell_traders_1m,
                                 volume_5m, buy_volume_5m, buy_volume_usd_5m, sell_volume_usd_5m, sell_volume_5m, volume_usd_5m, buy_txs_5m,
                                 sell_txs_5m, price_change_5m, traders_5m,
                                 buy_traders_5m,
                                 sell_traders_5m,
                                 volume_1h, buy_volume_1h, buy_volume_usd_1h, sell_volume_usd_1h, sell_volume_1h, volume_usd_1h, buy_txs_1h,
                                 sell_txs_1h, price_change_1h, traders_1h,
                                 buy_traders_1h,
                                 sell_traders_1h,
                                 volume_4h, buy_volume_4h, buy_volume_usd_4h, sell_volume_usd_4h, sell_volume_4h, volume_usd_4h, buy_txs_4h,
                                 sell_txs_4h, price_change_4h, traders_4h,
                                 buy_traders_4h,
                                 sell_traders_4h,
                                 volume_24h, buy_volume_24h, buy_volume_usd_24h, sell_volume_usd_24h, sell_volume_24h, volume_usd_24h, buy_txs_24h,
                                 sell_txs_24h, price_change_24h, traders_24h,
                                 buy_traders_24h,
                                 sell_traders_24h,
                                 volume_7d, buy_volume_7d, buy_volume_usd_7d, sell_volume_usd_7d, sell_volume_7d, volume_usd_7d, buy_txs_7d,
                                 sell_txs_7d, price_change_7d, traders_7d,
                                 buy_traders_7d,
                                 sell_traders_7d,
                                 extra,
                                 created_at, updated_at, risk_level,ts
                             )
                             VALUES
                             <foreach collection="list" item="item" separator=",">
                                 (
                                     #{item.platform}, #{item.address}, #{item.address}, #{item.name}, #{item.symbol}, #{item.symbol}, #{item.decimals},
                                     #{item.totalSupply}, #{item.burnSupply}, #{item.circulatingSupply}, #{item.holders},
                                     #{item.account},#{item.userAddress},
                                     #{item.publishAt}, #{item.launchedAt}, #{item.tradeAt}, #{item.creator}, #{item.owner},
                                     #{item.twitter}, #{item.website}, #{item.logoUrl}, #{item.imageUrl},
                                     #{item.telegram},
                                     #{item.poolSource},
                                     #{item.fdv}, #{item.marketcap}, #{item.liquidity},
                                     #{item.liquidityUsd}, #{item.liquidityScore}, #{item.confidenceScore}, #{item.price}, #{item.priceUsd}, #{item.priceHigh},
                                     #{item.priceLow}, #{item.auditPass}, #{item.liquidityAbnormal}, #{item.bondingCurveRatio}, #{item.totalTxs},
                                     #{item.volume1m}, #{item.buyVolume1m}, #{item.buyVolumeUsd1m}, #{item.sellVolumeUsd1m}, #{item.sellVolume1m}, #{item.volumeUsd1m}, #{item.buyTxs1m},
                                     #{item.sellTxs1m}, #{item.priceChange1m}, #{item.traders1m}, #{item.buyTraders1m}, #{item.sellTraders1m},
                                     #{item.volume5m}, #{item.buyVolume5m}, #{item.buyVolumeUsd5m}, #{item.sellVolumeUsd5m}, #{item.sellVolume5m}, #{item.volumeUsd5m}, #{item.buyTxs5m},
                                     #{item.sellTxs5m}, #{item.priceChange5m}, #{item.traders5m},
                                     #{item.buyTraders5m},
                                     #{item.sellTraders5m},
                                     #{item.volume1h}, #{item.buyVolume1h}, #{item.buyVolumeUsd1h}, #{item.sellVolumeUsd1h}, #{item.sellVolume1h}, #{item.volumeUsd1h}, #{item.buyTxs1h},
                                     #{item.sellTxs1h}, #{item.priceChange1h}, #{item.traders1h},
                                     #{item.buyTraders1h},
                                     #{item.sellTraders1h},
                                     #{item.volume4h}, #{item.buyVolume4h}, #{item.buyVolumeUsd4h}, #{item.sellVolumeUsd4h}, #{item.sellVolume4h}, #{item.volumeUsd4h}, #{item.buyTxs4h},
                                     #{item.sellTxs4h}, #{item.priceChange4h}, #{item.traders4h},
                                     #{item.buyTraders4h},
                                     #{item.sellTraders4h},
                                     #{item.volume24h}, #{item.buyVolume24h}, #{item.buyVolumeUsd24h}, #{item.sellVolumeUsd24h}, #{item.sellVolume24h}, #{item.volumeUsd24h}, #{item.buyTxs24h},
                                     #{item.sellTxs24h}, #{item.priceChange24h}, #{item.traders24h},
                                     #{item.buyTraders24h},
                                     #{item.sellTraders24h},
                                     #{item.volume7d}, #{item.buyVolume7d}, #{item.buyVolumeUsd7d}, #{item.sellVolumeUsd7d}, #{item.sellVolume7d}, #{item.volumeUsd7d}, #{item.buyTxs7d},
                                     #{item.sellTxs7d}, #{item.priceChange7d}, #{item.traders7d},
                                     #{item.buyTraders7d},
                                     #{item.sellTraders7d},
                                     #{item.extraInfo},
                                     #{item.createdAt}, #{item.updatedAt}, #{item.riskLevel}, #{item.ts}
                                 )
                             </foreach>
                             ON DUPLICATE KEY UPDATE
                             address_ci = IF(VALUES(address) IS NOT NULL, VALUES(address), address),
                             name = IF(VALUES(name) IS NOT NULL, VALUES(name), name),
                             symbol = IF(VALUES(symbol) IS NOT NULL, VALUES(symbol), symbol),
                             symbol_ci = IF(VALUES(symbol) IS NOT NULL, VALUES(symbol), symbol),
                             decimals = IF(VALUES(decimals) IS NOT NULL, VALUES(decimals), decimals),
                             total_supply = IF(VALUES(total_supply) IS NOT NULL, VALUES(total_supply), total_supply),
                             burn_supply = IF(VALUES(burn_supply) IS NOT NULL, VALUES(burn_supply), burn_supply),
                             circulating_supply = IF(VALUES(circulating_supply) IS NOT NULL, VALUES(circulating_supply), circulating_supply),
                             holders = IF(VALUES(holders) IS NOT NULL, VALUES(holders), holders),
                             account = IF(VALUES(account) IS NOT NULL, VALUES(account), account),
                             user_address = IF(VALUES(user_address) IS NOT NULL, VALUES(user_address), user_address),
                             publish_at = IF(VALUES(publish_at) IS NOT NULL, VALUES(publish_at), publish_at),
                             launched_at = IF(VALUES(launched_at) IS NOT NULL, VALUES(launched_at), launched_at),
                             trade_at = IF(VALUES(trade_at) IS NOT NULL, VALUES(trade_at), trade_at),
                             creator = IF(VALUES(creator) IS NOT NULL, VALUES(creator), creator),
                             owner = IF(VALUES(owner) IS NOT NULL, VALUES(owner), owner),
                             twitter = IF(VALUES(twitter) IS NOT NULL, VALUES(twitter), twitter),
                             website = IF(VALUES(website) IS NOT NULL, VALUES(website), website),
                             logo_url = IF(VALUES(logo_url) IS NOT NULL, VALUES(logo_url), logo_url),
                             image_url = IF(VALUES(image_url) IS NOT NULL, VALUES(image_url), image_url),
                             telegram = IF(VALUES(telegram) IS NOT NULL, VALUES(telegram), telegram),
                             pool_source = IF(VALUES(pool_source) IS NOT NULL, VALUES(pool_source), pool_source),
                             fdv = IF(VALUES(fdv) IS NOT NULL, VALUES(fdv), fdv),
                             marketcap = IF(VALUES(marketcap) IS NOT NULL, VALUES(marketcap), marketcap),
                             liquidity = IF(VALUES(liquidity) IS NOT NULL, VALUES(liquidity), liquidity),
                             liquidity_usd = IF(VALUES(liquidity_usd) IS NOT NULL, VALUES(liquidity_usd), liquidity_usd),
                             liquidity_score = IF(VALUES(liquidity_score) IS NOT NULL, VALUES(liquidity_score), liquidity_score),
                             confidence_score = IF(VALUES(confidence_score) IS NOT NULL, VALUES(confidence_score), confidence_score),
                             price = IF(VALUES(price) IS NOT NULL, VALUES(price), price),
                             price_usd = IF(VALUES(price_usd) IS NOT NULL, VALUES(price_usd), price_usd),
                             price_high = IF(VALUES(price_high) IS NOT NULL, VALUES(price_high), price_high),
                             price_low = IF(VALUES(price_low) IS NOT NULL, VALUES(price_low), price_low),
                             audit_pass = IF(VALUES(audit_pass) IS NOT NULL, VALUES(audit_pass), audit_pass),
                             liquidity_abnormal = IF(VALUES(liquidity_abnormal) IS NOT NULL, VALUES(liquidity_abnormal), liquidity_abnormal),
                             bonding_curve_ratio = IF(VALUES(bonding_curve_ratio) IS NOT NULL, VALUES(bonding_curve_ratio), bonding_curve_ratio),
                             total_txs = IF(VALUES(total_txs) IS NOT NULL, VALUES(total_txs), total_txs),
                             volume_1m = IF(VALUES(volume_1m) IS NOT NULL, VALUES(volume_1m), volume_1m),
                             buy_volume_1m = IF(VALUES(buy_volume_1m) IS NOT NULL, VALUES(buy_volume_1m), buy_volume_1m),
                             buy_volume_usd_1m = IF(VALUES(buy_volume_usd_1m) IS NOT NULL, VALUES(buy_volume_usd_1m), buy_volume_usd_1m),
                             sell_volume_usd_1m = IF(VALUES(sell_volume_usd_1m) IS NOT NULL, VALUES(sell_volume_usd_1m), sell_volume_usd_1m),
                             sell_volume_1m = IF(VALUES(sell_volume_1m) IS NOT NULL, VALUES(sell_volume_1m), sell_volume_1m),
                             volume_usd_1m = IF(VALUES(volume_usd_1m) IS NOT NULL, VALUES(volume_usd_1m), volume_usd_1m),
                             buy_txs_1m = IF(VALUES(buy_txs_1m) IS NOT NULL, VALUES(buy_txs_1m), buy_txs_1m),
                             sell_txs_1m = IF(VALUES(sell_txs_1m) IS NOT NULL, VALUES(sell_txs_1m), sell_txs_1m),
                             price_change_1m = IF(VALUES(price_change_1m) IS NOT NULL, VALUES(price_change_1m), price_change_1m),
                             traders_1m = IF(VALUES(traders_1m) IS NOT NULL, VALUES(traders_1m), traders_1m),
                             buy_traders_1m = IF(VALUES(buy_traders_1m) IS NOT NULL, VALUES(buy_traders_1m), buy_traders_1m),
                             sell_traders_1m = IF(VALUES(sell_traders_1m) IS NOT NULL, VALUES(sell_traders_1m), sell_traders_1m),
                             volume_5m = IF(VALUES(volume_5m) IS NOT NULL, VALUES(volume_5m), volume_5m),
                             buy_volume_5m = IF(VALUES(buy_volume_5m) IS NOT NULL, VALUES(buy_volume_5m), buy_volume_5m),
                             buy_volume_usd_5m = IF(VALUES(buy_volume_usd_5m) IS NOT NULL, VALUES(buy_volume_usd_5m), buy_volume_usd_5m),
                             sell_volume_usd_5m = IF(VALUES(sell_volume_usd_5m) IS NOT NULL, VALUES(sell_volume_usd_5m), sell_volume_usd_5m),
                             sell_volume_5m = IF(VALUES(sell_volume_5m) IS NOT NULL, VALUES(sell_volume_5m), sell_volume_5m),
                             volume_usd_5m = IF(VALUES(volume_usd_5m) IS NOT NULL, VALUES(volume_usd_5m), volume_usd_5m),
                             buy_txs_5m = IF(VALUES(buy_txs_5m) IS NOT NULL, VALUES(buy_txs_5m), buy_txs_5m),
                             sell_txs_5m = IF(VALUES(sell_txs_5m) IS NOT NULL, VALUES(sell_txs_5m), sell_txs_5m),
                             price_change_5m = IF(VALUES(price_change_5m) IS NOT NULL, VALUES(price_change_5m), price_change_5m),
                             traders_5m = IF(VALUES(traders_5m) IS NOT NULL, VALUES(traders_5m), traders_5m),
                             buy_traders_5m = IF(VALUES(buy_traders_5m) IS NOT NULL, VALUES(buy_traders_5m), buy_traders_5m),
                             sell_traders_5m = IF(VALUES(sell_traders_5m) IS NOT NULL, VALUES(sell_traders_5m), sell_traders_5m),
                             volume_1h = IF(VALUES(volume_1h) IS NOT NULL, VALUES(volume_1h), volume_1h),
                             buy_volume_1h = IF(VALUES(buy_volume_1h) IS NOT NULL, VALUES(buy_volume_1h), buy_volume_1h),
                             buy_volume_usd_1h = IF(VALUES(buy_volume_usd_1h) IS NOT NULL, VALUES(buy_volume_usd_1h), buy_volume_usd_1h),
                             sell_volume_usd_1h = IF(VALUES(sell_volume_usd_1h) IS NOT NULL, VALUES(sell_volume_usd_1h), sell_volume_usd_1h),
                             sell_volume_1h = IF(VALUES(sell_volume_1h) IS NOT NULL, VALUES(sell_volume_1h), sell_volume_1h),
                             volume_usd_1h = IF(VALUES(volume_usd_1h) IS NOT NULL, VALUES(volume_usd_1h), volume_usd_1h),
                             buy_txs_1h = IF(VALUES(buy_txs_1h) IS NOT NULL, VALUES(buy_txs_1h), buy_txs_1h),
                             sell_txs_1h = IF(VALUES(sell_txs_1h) IS NOT NULL, VALUES(sell_txs_1h), sell_txs_1h),
                             price_change_1h = IF(VALUES(price_change_1h) IS NOT NULL, VALUES(price_change_1h), price_change_1h),
                             traders_1h = IF(VALUES(traders_1h) IS NOT NULL, VALUES(traders_1h), traders_1h),
                             buy_traders_1h = IF(VALUES(buy_traders_1h) IS NOT NULL, VALUES(buy_traders_1h), buy_traders_1h),
                             sell_traders_1h = IF(VALUES(sell_traders_1h) IS NOT NULL, VALUES(sell_traders_1h), sell_traders_1h),
                             volume_4h = IF(VALUES(volume_4h) IS NOT NULL, VALUES(volume_4h), volume_4h),
                             buy_volume_4h = IF(VALUES(buy_volume_4h) IS NOT NULL, VALUES(buy_volume_4h), buy_volume_4h),
                             buy_volume_usd_4h = IF(VALUES(buy_volume_usd_4h) IS NOT NULL, VALUES(buy_volume_usd_4h), buy_volume_usd_4h),
                             sell_volume_usd_4h = IF(VALUES(sell_volume_usd_4h) IS NOT NULL, VALUES(sell_volume_usd_4h), sell_volume_usd_4h),
                             sell_volume_4h = IF(VALUES(sell_volume_4h) IS NOT NULL, VALUES(sell_volume_4h), sell_volume_4h),
                             volume_usd_4h = IF(VALUES(volume_usd_4h) IS NOT NULL, VALUES(volume_usd_4h), volume_usd_4h),
                             buy_txs_4h = IF(VALUES(buy_txs_4h) IS NOT NULL, VALUES(buy_txs_4h), buy_txs_4h),
                             sell_txs_4h = IF(VALUES(sell_txs_4h) IS NOT NULL, VALUES(sell_txs_4h), sell_txs_4h),
                             price_change_4h = IF(VALUES(price_change_4h) IS NOT NULL, VALUES(price_change_4h), price_change_4h),
                             traders_4h = IF(VALUES(traders_4h) IS NOT NULL, VALUES(traders_4h), traders_4h),
                             buy_traders_4h = IF(VALUES(buy_traders_4h) IS NOT NULL, VALUES(buy_traders_4h), buy_traders_4h),
                             sell_traders_4h = IF(VALUES(sell_traders_4h) IS NOT NULL, VALUES(sell_traders_4h), sell_traders_4h),
                             volume_24h = IF(VALUES(volume_24h) IS NOT NULL, VALUES(volume_24h), volume_24h),
                             buy_volume_24h = IF(VALUES(buy_volume_24h) IS NOT NULL, VALUES(buy_volume_24h), buy_volume_24h),
                             buy_volume_usd_24h = IF(VALUES(buy_volume_usd_24h) IS NOT NULL, VALUES(buy_volume_usd_24h), buy_volume_usd_24h),
                             sell_volume_usd_24h = IF(VALUES(sell_volume_usd_24h) IS NOT NULL, VALUES(sell_volume_usd_24h), sell_volume_usd_24h),
                             sell_volume_24h = IF(VALUES(sell_volume_24h) IS NOT NULL, VALUES(sell_volume_24h), sell_volume_24h),
                             volume_usd_24h = IF(VALUES(volume_usd_24h) IS NOT NULL, VALUES(volume_usd_24h), volume_usd_24h),
                             buy_txs_24h = IF(VALUES(buy_txs_24h) IS NOT NULL, VALUES(buy_txs_24h), buy_txs_24h),
                             sell_txs_24h = IF(VALUES(sell_txs_24h) IS NOT NULL, VALUES(sell_txs_24h), sell_txs_24h),
                             price_change_24h = IF(VALUES(price_change_24h) IS NOT NULL, VALUES(price_change_24h), price_change_24h),
                             traders_24h = IF(VALUES(traders_24h) IS NOT NULL, VALUES(traders_24h), traders_24h),
                             buy_traders_24h = IF(VALUES(buy_traders_24h) IS NOT NULL, VALUES(buy_traders_24h), buy_traders_24h),
                             sell_traders_24h = IF(VALUES(sell_traders_24h) IS NOT NULL, VALUES(sell_traders_24h), sell_traders_24h),
                             volume_7d = IF(VALUES(volume_7d) IS NOT NULL, VALUES(volume_7d), volume_7d),
                             buy_volume_7d = IF(VALUES(buy_volume_7d) IS NOT NULL, VALUES(buy_volume_7d), buy_volume_7d),
                             buy_volume_usd_7d = IF(VALUES(buy_volume_usd_7d) IS NOT NULL, VALUES(buy_volume_usd_7d), buy_volume_usd_7d),
                             sell_volume_usd_7d = IF(VALUES(sell_volume_usd_7d) IS NOT NULL, VALUES(sell_volume_usd_7d), sell_volume_usd_7d),
                             sell_volume_7d = IF(VALUES(sell_volume_7d) IS NOT NULL, VALUES(sell_volume_7d), sell_volume_7d),
                             volume_usd_7d = IF(VALUES(volume_usd_7d) IS NOT NULL, VALUES(volume_usd_7d), volume_usd_7d),
                             buy_txs_7d = IF(VALUES(buy_txs_7d) IS NOT NULL, VALUES(buy_txs_7d), buy_txs_7d),
                             sell_txs_7d = IF(VALUES(sell_txs_7d) IS NOT NULL, VALUES(sell_txs_7d), sell_txs_7d),
                             price_change_7d = IF(VALUES(price_change_7d) IS NOT NULL, VALUES(price_change_7d), price_change_7d),
                             traders_7d = IF(VALUES(traders_7d) IS NOT NULL, VALUES(traders_7d), traders_7d),
                             buy_traders_7d = IF(VALUES(buy_traders_7d) IS NOT NULL, VALUES(buy_traders_7d), buy_traders_7d),
                             sell_traders_7d = IF(VALUES(sell_traders_7d) IS NOT NULL, VALUES(sell_traders_7d), sell_traders_7d),
                             created_at = IF(VALUES(created_at) IS NOT NULL, VALUES(created_at), created_at),
                             updated_at = IF(VALUES(updated_at) IS NOT NULL, VALUES(updated_at), updated_at),
                             risk_level = IF(VALUES(risk_level) IS NOT NULL, VALUES(risk_level), risk_level),
                             extra = IF(VALUES(extra) IS NOT NULL, VALUES(extra), extra),
                             ts = IF(VALUES(ts) IS NOT NULL, VALUES(ts), ts)
                    </script>
            """)
    void batchInsertOrUpdateMetaDataForMigrateDta(@Param("list") List<DexTokenDetailEntity> list);

    @Insert("""
                    <script>
                       INSERT INTO dex_token_detail (
                                 platform, address, address_ci, name, symbol, symbol_ci, decimals, total_supply, burn_supply,
                                 circulating_supply, holders,
                                 account,user_address,
                                 publish_at, launched_at, trade_at, creator,
                                 owner, twitter, website, logo_url, image_url,
                                 telegram,
                                 pool_source,
                                 fdv, marketcap, liquidity,
                                 liquidity_usd, liquidity_score, confidence_score, price, price_usd, price_high,
                                 price_low, audit_pass, liquidity_abnormal, bonding_curve_ratio, total_txs,
                                 volume_1m, buy_volume_1m, buy_volume_usd_1m, sell_volume_usd_1m, sell_volume_1m, volume_usd_1m, txs_1m, buy_txs_1m,
                                 sell_txs_1m, price_change_1m, traders_1m, buy_traders_1m, sell_traders_1m,
                                 volume_5m, buy_volume_5m, buy_volume_usd_5m, sell_volume_usd_5m, sell_volume_5m, volume_usd_5m, txs_5m, buy_txs_5m,
                                 sell_txs_5m, price_change_5m, traders_5m,
                                 buy_traders_5m,
                                 sell_traders_5m,
                                 volume_1h, buy_volume_1h, buy_volume_usd_1h, sell_volume_usd_1h, sell_volume_1h, volume_usd_1h, txs_1h, buy_txs_1h,
                                 sell_txs_1h, price_change_1h, traders_1h,
                                 buy_traders_1h,
                                 sell_traders_1h,
                                 volume_4h, buy_volume_4h, buy_volume_usd_4h, sell_volume_usd_4h, sell_volume_4h, volume_usd_4h, txs_4h, buy_txs_4h,
                                 sell_txs_4h, price_change_4h, traders_4h,
                                 buy_traders_4h,
                                 sell_traders_4h,
                                 volume_24h, buy_volume_24h, buy_volume_usd_24h, sell_volume_usd_24h, sell_volume_24h, volume_usd_24h, txs_24h, buy_txs_24h,
                                 sell_txs_24h, price_change_24h, traders_24h,
                                 buy_traders_24h,
                                 sell_traders_24h,
                                 volume_7d, buy_volume_7d, buy_volume_usd_7d, sell_volume_usd_7d, sell_volume_7d, volume_usd_7d, txs_7d, buy_txs_7d,
                                 sell_txs_7d, price_change_7d, traders_7d,
                                 buy_traders_7d,
                                 sell_traders_7d,
                                 extra,risk_level,ts
                             )
                             VALUES
                             <foreach collection="list" item="item" separator=",">
                                 (
                                     #{item.platform}, #{item.address}, #{item.address}, #{item.name}, #{item.symbol}, #{item.symbol}, #{item.decimals},
                                     #{item.totalSupply}, #{item.burnSupply}, #{item.circulatingSupply}, #{item.holders},
                                     #{item.account},#{item.userAddress},
                                     #{item.publishAt}, #{item.launchedAt}, #{item.tradeAt}, #{item.creator}, #{item.owner},
                                     #{item.twitter}, #{item.website}, #{item.logoUrl}, #{item.imageUrl},
                                     #{item.telegram},
                                     #{item.poolSource},
                                     #{item.fdv}, #{item.marketcap}, #{item.liquidity},
                                     #{item.liquidityUsd}, #{item.liquidityScore}, #{item.confidenceScore}, #{item.price}, #{item.priceUsd}, #{item.priceHigh},
                                     #{item.priceLow}, #{item.auditPass}, #{item.liquidityAbnormal}, #{item.bondingCurveRatio}, #{item.totalTxs},
                                     #{item.volume1m}, #{item.buyVolume1m}, #{item.buyVolumeUsd1m}, #{item.sellVolumeUsd1m}, #{item.sellVolume1m}, #{item.volumeUsd1m}, #{item.txs1m}, #{item.buyTxs1m},
                                     #{item.sellTxs1m}, #{item.priceChange1m}, #{item.traders1m}, #{item.buyTraders1m}, #{item.sellTraders1m},
                                     #{item.volume5m}, #{item.buyVolume5m}, #{item.buyVolumeUsd5m}, #{item.sellVolumeUsd5m}, #{item.sellVolume5m}, #{item.volumeUsd5m}, #{item.txs5m}, #{item.buyTxs5m},
                                     #{item.sellTxs5m}, #{item.priceChange5m}, #{item.traders5m},
                                     #{item.buyTraders5m},
                                     #{item.sellTraders5m},
                                     #{item.volume1h}, #{item.buyVolume1h}, #{item.buyVolumeUsd1h}, #{item.sellVolumeUsd1h}, #{item.sellVolume1h}, #{item.volumeUsd1h}, #{item.txs1h}, #{item.buyTxs1h},
                                     #{item.sellTxs1h}, #{item.priceChange1h}, #{item.traders1h},
                                     #{item.buyTraders1h},
                                     #{item.sellTraders1h},
                                     #{item.volume4h}, #{item.buyVolume4h}, #{item.buyVolumeUsd4h}, #{item.sellVolumeUsd4h}, #{item.sellVolume4h}, #{item.volumeUsd4h}, #{item.txs4h}, #{item.buyTxs4h},
                                     #{item.sellTxs4h}, #{item.priceChange4h}, #{item.traders4h},
                                     #{item.buyTraders4h},
                                     #{item.sellTraders4h},
                                     #{item.volume24h}, #{item.buyVolume24h}, #{item.buyVolumeUsd24h}, #{item.sellVolumeUsd24h}, #{item.sellVolume24h}, #{item.volumeUsd24h}, #{item.txs24h}, #{item.buyTxs24h},
                                     #{item.sellTxs24h}, #{item.priceChange24h}, #{item.traders24h},
                                     #{item.buyTraders24h},
                                     #{item.sellTraders24h},
                                     #{item.volume7d}, #{item.buyVolume7d}, #{item.buyVolumeUsd7d}, #{item.sellVolumeUsd7d}, #{item.sellVolume7d}, #{item.volumeUsd7d}, #{item.txs7d}, #{item.buyTxs7d},
                                     #{item.sellTxs7d}, #{item.priceChange7d}, #{item.traders7d},
                                     #{item.buyTraders7d},
                                     #{item.sellTraders7d},
                                     #{item.extraInfo},
                                     #{item.riskLevel}, #{item.ts}
                                 )
                             </foreach>
                             ON DUPLICATE KEY UPDATE
                             address_ci = IF(VALUES(address) IS NOT NULL, VALUES(address), address),
                             name = IF(VALUES(name) IS NOT NULL, VALUES(name), name),
                             symbol = IF(VALUES(symbol) IS NOT NULL, VALUES(symbol), symbol),
                             symbol_ci = IF(VALUES(symbol) IS NOT NULL, VALUES(symbol), symbol),
                             decimals = IF(VALUES(decimals) IS NOT NULL, VALUES(decimals), decimals),
                             total_supply = IF(VALUES(total_supply) IS NOT NULL, VALUES(total_supply), total_supply),
                             burn_supply = IF(VALUES(burn_supply) IS NOT NULL, VALUES(burn_supply), burn_supply),
                             circulating_supply = IF(VALUES(circulating_supply) IS NOT NULL, VALUES(circulating_supply), circulating_supply),
                             holders = IF(VALUES(holders) IS NOT NULL, VALUES(holders), holders),
                             account = IF(VALUES(account) IS NOT NULL, VALUES(account), account),
                             user_address = IF(VALUES(user_address) IS NOT NULL, VALUES(user_address), user_address),
                             publish_at = IF(VALUES(publish_at) IS NOT NULL AND publish_at IS NULL, VALUES(publish_at), publish_at),
                             launched_at = IF(VALUES(launched_at) IS NOT NULL, VALUES(launched_at), launched_at),
                             trade_at = IF(VALUES(trade_at) IS NOT NULL, VALUES(trade_at), trade_at),
                             creator = IF(VALUES(creator) IS NOT NULL, VALUES(creator), creator),
                             owner = IF(VALUES(owner) IS NOT NULL, VALUES(owner), owner),
                             twitter = IF(VALUES(twitter) IS NOT NULL, VALUES(twitter), twitter),
                             website = IF(VALUES(website) IS NOT NULL, VALUES(website), website),
                             logo_url = IF(VALUES(logo_url) IS NOT NULL, VALUES(logo_url), logo_url),
                             image_url = IF(VALUES(image_url) IS NOT NULL, VALUES(image_url), image_url),
                             telegram = IF(VALUES(telegram) IS NOT NULL, VALUES(telegram), telegram),
                             pool_source = IF(VALUES(pool_source) IS NOT NULL, VALUES(pool_source), pool_source),
                             fdv = IF(VALUES(fdv) IS NOT NULL, VALUES(fdv), fdv),
                             marketcap = IF(VALUES(marketcap) IS NOT NULL, VALUES(marketcap), marketcap),
                             liquidity = IF(VALUES(liquidity) IS NOT NULL, VALUES(liquidity), liquidity),
                             liquidity_usd = IF(VALUES(liquidity_usd) IS NOT NULL, VALUES(liquidity_usd), liquidity_usd),
                             liquidity_score = IF(VALUES(liquidity_score) IS NOT NULL, VALUES(liquidity_score), liquidity_score),
                             confidence_score = IF(VALUES(confidence_score) IS NOT NULL, VALUES(confidence_score), confidence_score),
                             price = IF(VALUES(price) IS NOT NULL, VALUES(price), price),
                             price_usd = IF(VALUES(price_usd) IS NOT NULL AND VALUES(ts) IS NOT NULL AND VALUES(ts) > ts, VALUES(price_usd), price_usd),
                             price_high = IF(VALUES(price_high) IS NOT NULL, VALUES(price_high), price_high),
                             price_low = IF(VALUES(price_low) IS NOT NULL, VALUES(price_low), price_low),
                             audit_pass = IF(VALUES(audit_pass) IS NOT NULL, VALUES(audit_pass), audit_pass),
                             liquidity_abnormal = IF(VALUES(liquidity_abnormal) IS NOT NULL, VALUES(liquidity_abnormal), liquidity_abnormal),
                             bonding_curve_ratio = IF(VALUES(bonding_curve_ratio) IS NOT NULL, VALUES(bonding_curve_ratio), bonding_curve_ratio),
                             total_txs = IF(VALUES(total_txs) IS NOT NULL, VALUES(total_txs), total_txs),
                             volume_1m = IF(VALUES(volume_1m) IS NOT NULL, VALUES(volume_1m), volume_1m),
                             buy_volume_1m = IF(VALUES(buy_volume_1m) IS NOT NULL, VALUES(buy_volume_1m), buy_volume_1m),
                             buy_volume_usd_1m = IF(VALUES(buy_volume_usd_1m) IS NOT NULL, VALUES(buy_volume_usd_1m), buy_volume_usd_1m),
                             sell_volume_usd_1m = IF(VALUES(sell_volume_usd_1m) IS NOT NULL, VALUES(sell_volume_usd_1m), sell_volume_usd_1m),
                             sell_volume_1m = IF(VALUES(sell_volume_1m) IS NOT NULL, VALUES(sell_volume_1m), sell_volume_1m),
                             volume_usd_1m = IF(VALUES(volume_usd_1m) IS NOT NULL, VALUES(volume_usd_1m), volume_usd_1m),
                             txs_1m = IF(VALUES(txs_1m) IS NOT NULL, VALUES(txs_1m), txs_1m),
                             buy_txs_1m = IF(VALUES(buy_txs_1m) IS NOT NULL, VALUES(buy_txs_1m), buy_txs_1m),
                             sell_txs_1m = IF(VALUES(sell_txs_1m) IS NOT NULL, VALUES(sell_txs_1m), sell_txs_1m),
                             price_change_1m = IF(VALUES(price_change_1m) IS NOT NULL, VALUES(price_change_1m), price_change_1m),
                             traders_1m = IF(VALUES(traders_1m) IS NOT NULL, VALUES(traders_1m), traders_1m),
                             buy_traders_1m = IF(VALUES(buy_traders_1m) IS NOT NULL, VALUES(buy_traders_1m), buy_traders_1m),
                             sell_traders_1m = IF(VALUES(sell_traders_1m) IS NOT NULL, VALUES(sell_traders_1m), sell_traders_1m),
                             volume_5m = IF(VALUES(volume_5m) IS NOT NULL, VALUES(volume_5m), volume_5m),
                             buy_volume_5m = IF(VALUES(buy_volume_5m) IS NOT NULL, VALUES(buy_volume_5m), buy_volume_5m),
                             buy_volume_usd_5m = IF(VALUES(buy_volume_usd_5m) IS NOT NULL, VALUES(buy_volume_usd_5m), buy_volume_usd_5m),
                             sell_volume_usd_5m = IF(VALUES(sell_volume_usd_5m) IS NOT NULL, VALUES(sell_volume_usd_5m), sell_volume_usd_5m),
                             sell_volume_5m = IF(VALUES(sell_volume_5m) IS NOT NULL, VALUES(sell_volume_5m), sell_volume_5m),
                             volume_usd_5m = IF(VALUES(volume_usd_5m) IS NOT NULL, VALUES(volume_usd_5m), volume_usd_5m),
                             txs_5m = IF(VALUES(txs_5m) IS NOT NULL, VALUES(txs_5m), txs_5m),
                             buy_txs_5m = IF(VALUES(buy_txs_5m) IS NOT NULL, VALUES(buy_txs_5m), buy_txs_5m),
                             sell_txs_5m = IF(VALUES(sell_txs_5m) IS NOT NULL, VALUES(sell_txs_5m), sell_txs_5m),
                             price_change_5m = IF(VALUES(price_change_5m) IS NOT NULL, VALUES(price_change_5m), price_change_5m),
                             traders_5m = IF(VALUES(traders_5m) IS NOT NULL, VALUES(traders_5m), traders_5m),
                             buy_traders_5m = IF(VALUES(buy_traders_5m) IS NOT NULL, VALUES(buy_traders_5m), buy_traders_5m),
                             sell_traders_5m = IF(VALUES(sell_traders_5m) IS NOT NULL, VALUES(sell_traders_5m), sell_traders_5m),
                             volume_1h = IF(VALUES(volume_1h) IS NOT NULL, VALUES(volume_1h), volume_1h),
                             buy_volume_1h = IF(VALUES(buy_volume_1h) IS NOT NULL, VALUES(buy_volume_1h), buy_volume_1h),
                             buy_volume_usd_1h = IF(VALUES(buy_volume_usd_1h) IS NOT NULL, VALUES(buy_volume_usd_1h), buy_volume_usd_1h),
                             sell_volume_usd_1h = IF(VALUES(sell_volume_usd_1h) IS NOT NULL, VALUES(sell_volume_usd_1h), sell_volume_usd_1h),
                             sell_volume_1h = IF(VALUES(sell_volume_1h) IS NOT NULL, VALUES(sell_volume_1h), sell_volume_1h),
                             volume_usd_1h = IF(VALUES(volume_usd_1h) IS NOT NULL, VALUES(volume_usd_1h), volume_usd_1h),
                             txs_1h = IF(VALUES(txs_1h) IS NOT NULL, VALUES(txs_1h), txs_1h),
                             buy_txs_1h = IF(VALUES(buy_txs_1h) IS NOT NULL, VALUES(buy_txs_1h), buy_txs_1h),
                             sell_txs_1h = IF(VALUES(sell_txs_1h) IS NOT NULL, VALUES(sell_txs_1h), sell_txs_1h),
                             price_change_1h = IF(VALUES(price_change_1h) IS NOT NULL, VALUES(price_change_1h), price_change_1h),
                             traders_1h = IF(VALUES(traders_1h) IS NOT NULL, VALUES(traders_1h), traders_1h),
                             buy_traders_1h = IF(VALUES(buy_traders_1h) IS NOT NULL, VALUES(buy_traders_1h), buy_traders_1h),
                             sell_traders_1h = IF(VALUES(sell_traders_1h) IS NOT NULL, VALUES(sell_traders_1h), sell_traders_1h),
                             volume_4h = IF(VALUES(volume_4h) IS NOT NULL, VALUES(volume_4h), volume_4h),
                             buy_volume_4h = IF(VALUES(buy_volume_4h) IS NOT NULL, VALUES(buy_volume_4h), buy_volume_4h),
                             buy_volume_usd_4h = IF(VALUES(buy_volume_usd_4h) IS NOT NULL, VALUES(buy_volume_usd_4h), buy_volume_usd_4h),
                             sell_volume_usd_4h = IF(VALUES(sell_volume_usd_4h) IS NOT NULL, VALUES(sell_volume_usd_4h), sell_volume_usd_4h),
                             sell_volume_4h = IF(VALUES(sell_volume_4h) IS NOT NULL, VALUES(sell_volume_4h), sell_volume_4h),
                             volume_usd_4h = IF(VALUES(volume_usd_4h) IS NOT NULL, VALUES(volume_usd_4h), volume_usd_4h),
                             txs_4h = IF(VALUES(txs_4h) IS NOT NULL, VALUES(txs_4h), txs_4h),
                             buy_txs_4h = IF(VALUES(buy_txs_4h) IS NOT NULL, VALUES(buy_txs_4h), buy_txs_4h),
                             sell_txs_4h = IF(VALUES(sell_txs_4h) IS NOT NULL, VALUES(sell_txs_4h), sell_txs_4h),
                             price_change_4h = IF(VALUES(price_change_4h) IS NOT NULL, VALUES(price_change_4h), price_change_4h),
                             traders_4h = IF(VALUES(traders_4h) IS NOT NULL, VALUES(traders_4h), traders_4h),
                             buy_traders_4h = IF(VALUES(buy_traders_4h) IS NOT NULL, VALUES(buy_traders_4h), buy_traders_4h),
                             sell_traders_4h = IF(VALUES(sell_traders_4h) IS NOT NULL, VALUES(sell_traders_4h), sell_traders_4h),
                             volume_24h = IF(VALUES(volume_24h) IS NOT NULL, VALUES(volume_24h), volume_24h),
                             buy_volume_24h = IF(VALUES(buy_volume_24h) IS NOT NULL, VALUES(buy_volume_24h), buy_volume_24h),
                             buy_volume_usd_24h = IF(VALUES(buy_volume_usd_24h) IS NOT NULL, VALUES(buy_volume_usd_24h), buy_volume_usd_24h),
                             sell_volume_usd_24h = IF(VALUES(sell_volume_usd_24h) IS NOT NULL, VALUES(sell_volume_usd_24h), sell_volume_usd_24h),
                             sell_volume_24h = IF(VALUES(sell_volume_24h) IS NOT NULL, VALUES(sell_volume_24h), sell_volume_24h),
                             volume_usd_24h = IF(VALUES(volume_usd_24h) IS NOT NULL, VALUES(volume_usd_24h), volume_usd_24h),
                             txs_24h = IF(VALUES(txs_24h) IS NOT NULL, VALUES(txs_24h), txs_24h),
                             buy_txs_24h = IF(VALUES(buy_txs_24h) IS NOT NULL, VALUES(buy_txs_24h), buy_txs_24h),
                             sell_txs_24h = IF(VALUES(sell_txs_24h) IS NOT NULL, VALUES(sell_txs_24h), sell_txs_24h),
                             price_change_24h = IF(VALUES(price_change_24h) IS NOT NULL, VALUES(price_change_24h), price_change_24h),
                             traders_24h = IF(VALUES(traders_24h) IS NOT NULL, VALUES(traders_24h), traders_24h),
                             buy_traders_24h = IF(VALUES(buy_traders_24h) IS NOT NULL, VALUES(buy_traders_24h), buy_traders_24h),
                             sell_traders_24h = IF(VALUES(sell_traders_24h) IS NOT NULL, VALUES(sell_traders_24h), sell_traders_24h),
                             volume_7d = IF(VALUES(volume_7d) IS NOT NULL, VALUES(volume_7d), volume_7d),
                             buy_volume_7d = IF(VALUES(buy_volume_7d) IS NOT NULL, VALUES(buy_volume_7d), buy_volume_7d),
                             buy_volume_usd_7d = IF(VALUES(buy_volume_usd_7d) IS NOT NULL, VALUES(buy_volume_usd_7d), buy_volume_usd_7d),
                             sell_volume_usd_7d = IF(VALUES(sell_volume_usd_7d) IS NOT NULL, VALUES(sell_volume_usd_7d), sell_volume_usd_7d),
                             sell_volume_7d = IF(VALUES(sell_volume_7d) IS NOT NULL, VALUES(sell_volume_7d), sell_volume_7d),
                             volume_usd_7d = IF(VALUES(volume_usd_7d) IS NOT NULL, VALUES(volume_usd_7d), volume_usd_7d),
                             txs_7d = IF(VALUES(txs_7d) IS NOT NULL, VALUES(txs_7d), txs_7d),
                             buy_txs_7d = IF(VALUES(buy_txs_7d) IS NOT NULL, VALUES(buy_txs_7d), buy_txs_7d),
                             sell_txs_7d = IF(VALUES(sell_txs_7d) IS NOT NULL, VALUES(sell_txs_7d), sell_txs_7d),
                             price_change_7d = IF(VALUES(price_change_7d) IS NOT NULL, VALUES(price_change_7d), price_change_7d),
                             traders_7d = IF(VALUES(traders_7d) IS NOT NULL, VALUES(traders_7d), traders_7d),
                             buy_traders_7d = IF(VALUES(buy_traders_7d) IS NOT NULL, VALUES(buy_traders_7d), buy_traders_7d),
                             sell_traders_7d = IF(VALUES(sell_traders_7d) IS NOT NULL, VALUES(sell_traders_7d), sell_traders_7d),
                             risk_level = IF(VALUES(risk_level) IS NOT NULL, VALUES(risk_level), risk_level),
                             extra = IF(VALUES(extra) IS NOT NULL, VALUES(extra), extra),
                             ts = IF(VALUES(ts) IS NOT NULL, VALUES(ts), ts)
                    </script>
            """)
    void batchInsertOrUpdateData(@Param("list") List<DexTokenDetailEntity> list);


    @Select("""
            <script>
            SELECT * FROM dex_token_detail
                   WHERE platform = #{entity.platform}
                        AND liquidity_usd >= #{entity.gteLiquidity}
                        <if test="entity.gteMc != null">
                            AND marketcap >= #{entity.gteMc}
                        </if>
                        <if test="entity.gteVolume5m != null">
                            AND volume_usd_5m >= #{entity.gteVolume5m}
                        </if>
                        <if test="entity.gteTxs5m != null">
                            AND txs_5m >= #{entity.gteTxs5m}
                        </if>
                        <if test="entity.gteVolume1h != null">
                            AND volume_usd_1h >= #{entity.gteVolume1h}
                        </if>
                        <if test="entity.gteTxs1h != null">
                            AND txs_1h >= #{entity.gteTxs1h}
                        </if>
                        <if test="entity.gteVolume4h != null">
                            AND volume_usd_4h >= #{entity.gteVolume4h}
                        </if>
                        <if test="entity.gteTxs4h != null">
                            AND txs_4h >= #{entity.gteTxs4h}
                        </if>
                        <if test="entity.gteVolume24h != null">
                            AND volume_usd_24h >= #{entity.gteVolume24h}
                        </if>
                        <if test="entity.gteTxs24h != null">
                            AND txs_24h >= #{entity.gteTxs24h}
                        </if>
                       <if test="entity.lastAddress != null">
                            AND address &lt; #{entity.lastAddress}
                        </if>
            ORDER BY address DESC,liquidity_usd DESC
            LIMIT #{entity.limit}
            </script>
            """)
    List<DexTokenDetailEntity> queryDetailForTrending(@Param("entity") DexTokenDetailQueryEntity queryEntity);


    @Select("""
            <script>
            SELECT * FROM dex_token_detail
                WHERE platform = #{platform}
                AND address IN
                    <foreach item='addr' collection='addressList' open='(' separator=',' close=')'>
                        #{addr}
                    </foreach>
            </script>
            """)
    List<DexTokenDetailEntity> queryByPlatformAndAddressList(
            @Param("platform") Integer platform,
            @Param("addressList") List<String> addressList
    );

    @Select("""
                <script>
                    SELECT platform, address, publish_at AS publishAt,
                           marketcap, liquidity_usd as liquidityUsd, audit_pass AS auditPass,
                           volume_usd_5m AS volumeUsd5m, txs_5m AS txs5m, buy_txs_5m AS buyTxs5m, sell_txs_5m AS sellTxs5m,
                           price_change_5m AS priceChange5m,
                           volume_usd_1h AS volumeUsd1h, txs_1h AS txs1h, buy_txs_1h AS buyTxs1h, sell_txs_1h AS sellTxs1h,
                           price_change_1h AS priceChange1h,
                           volume_usd_4h AS volumeUsd4h, buy_volume_4h AS buyVolume4h, sell_volume_4h AS sellVolume4h,
                           txs_4h AS txs4h, buy_txs_4h AS buyTxs4h, sell_txs_4h AS sellTxs4h,
                           price_change_4h AS priceChange4h,
                           volume_usd_24h AS volumeUsd24h, txs_24h AS txs24h, buy_txs_24h AS buyTxs24h, sell_txs_24h AS sellTxs24h,
                           price_change_24h AS priceChange24h,
                           created_at AS createdAt, updated_at AS updatedAt,
                           twitter, website, telegram,risk_level,pool_source
                    FROM dex_token_detail
                    <if test="forceIndex != null and forceIndex">
                            FORCE INDEX (${tableIndex})
                        </if>
                    WHERE volume_usd_24h &gt; #{volumeUsd24h}
                      AND price_change_24h &gt; #{priceChange24h}
                      <if test="lastAddress != null">
                        AND address &gt; #{lastAddress}
                      </if>
                    ORDER BY address,volume_usd_24h, price_change_24h
                    LIMIT #{limit}
                </script>
            """)
    List<DexTokenDetailEntity> pageDexGainerDTOByAddress(@Param("volumeUsd24h") BigDecimal volumeUsd24h, @Param("priceChange24h") BigDecimal priceChange24h, @Param("lastAddress") String lastAddress, @Param("limit") int limit,
                                                         @Param("forceIndex") Boolean forceIndex, @Param("tableIndex") String tableIndex);

    @Select("""
        <script>
            SELECT platform, name, symbol, address, publish_at, launched_at, 
                   website, twitter, logo_url, price_usd, price_change_24h, 
                   volume_usd_24h, fdv, marketcap, liquidity_usd
            FROM dex_token_detail
            WHERE address_ci = #{keyword}
            <if test='platformId != null'>
                AND platform = #{platformId}
            </if>
            <if test='blackPlatformIds != null and blackPlatformIds.size() > 0'>
                AND platform NOT IN 
                <foreach collection='blackPlatformIds' item='item' open='(' separator=',' close=')'>
                    #{item}
                </foreach>
            </if>
            <if test='sort != null'>
                ORDER BY ${sort} ${order}
            </if>
            LIMIT #{limit}
        </script>
        """)
    List<DexTokenDetailEntity> queryByExactAddress(@Param("keyword") String keyword,
        @Param("platformId") Integer platformId, @Param("sort") String sort, @Param("order") String order,
        @Param("limit") Integer limit, @Param("blackPlatformIds") List<Integer> blackPlatformIds);

    @Select("""
        <script>
            SELECT platform, name, symbol, address, publish_at, launched_at, 
                   website, twitter, logo_url, price_usd, price_change_24h, 
                   volume_usd_24h, fdv, marketcap, liquidity_usd, ts
            FROM dex_token_detail
            WHERE symbol_ci LIKE CONCAT(#{keyword}, '%')
            <if test='platformId != null'>
                AND platform = #{platformId}
            </if>
            <if test='blackPlatformIds != null and blackPlatformIds.size() > 0'>
                AND platform NOT IN 
                <foreach collection='blackPlatformIds' item='item' open='(' separator=',' close=')'>
                    #{item}
                </foreach>
            </if>
            <if test='sort != null'>
                ORDER BY ${sort} ${order}
            </if>
            LIMIT #{limit}
        </script>
        """)
    List<DexTokenDetailEntity> queryBySymbolPrefix(@Param("keyword") String keyword,
        @Param("platformId") Integer platformId, @Param("sort") String sort, @Param("order") String order,
        @Param("limit") Integer limit, @Param("blackPlatformIds") List<Integer> blackPlatformIds);

    @Select("""
        <script>
            SELECT platform, name, symbol, address, publish_at, launched_at, 
                   website, twitter, logo_url, price_usd, price_change_24h, 
                   volume_usd_24h, fdv, marketcap, liquidity_usd, ts
            FROM dex_token_detail
            WHERE address_ci = #{keyword}
            <if test='platformId != null'>
                AND platform = #{platformId}
            </if>
            <if test='blackPlatformIds != null and blackPlatformIds.size() > 0'>
                AND platform NOT IN 
                <foreach collection='blackPlatformIds' item='item' open='(' separator=',' close=')'>
                    #{item}
                </foreach>
            </if>
            UNION ALL
            SELECT platform, name, symbol, address, publish_at, launched_at, 
                   website, twitter, logo_url, price_usd, price_change_24h, 
                   volume_usd_24h, fdv, marketcap, liquidity_usd, ts
            FROM dex_token_detail
            WHERE symbol_ci LIKE CONCAT(#{keyword}, '%')
            <if test='platformId != null'>
                AND platform = #{platformId}
            </if>
            <if test='blackPlatformIds != null and blackPlatformIds.size() > 0'>
                AND platform NOT IN 
                <foreach collection='blackPlatformIds' item='item' open='(' separator=',' close=')'>
                    #{item}
                </foreach>
            </if>
            <if test='sort != null'>
                ORDER BY ${sort} ${order}
            </if>
            LIMIT #{limit}
        </script>
        """)
    List<DexTokenDetailEntity> queryByAddressAndSymbol(@Param("keyword") String keyword,
        @Param("platformId") Integer platformId, @Param("sort") String sort, @Param("order") String order,
        @Param("limit") Integer limit, @Param("blackPlatformIds") List<Integer> blackPlatformIds);

    @Select("""
            <script>
                SELECT platform, address
                FROM dex_token_detail
                WHERE platform = #{platformId} ORDER BY address
                LIMIT #{limit} OFFSET #{offset}
            </script>
            """)
    List<DexTokenDetailEntity> queryAllPlatformAndAddress(@Param("platformId") Integer platformId,
                                                          @Param("offset") Integer offset, @Param("limit") Integer limit);

    @Select("""
            <script>
              SELECT COUNT(1)
                 FROM dex_token_detail                  
            </script>
            """)
    Long queryAllCount();


    @Select("""
           <script>
              SELECT COUNT(DISTINCT address)
                 FROM dex_token_detail
                 where  platform = #{platformId}          
            </script>
            """)
    Long queryTokenCount(Integer platformId);

    /**
     * Batch update symbol_ci and address_ci fields
     *
     * @param limit maximum number of rows to update
     * @return number of affected rows
     */
    @Update("""
                UPDATE dex_token_detail
                SET symbol_ci = symbol, address_ci = address
                WHERE symbol_ci = ''
                LIMIT #{limit}
            """)
    int batchUpdateSymbolAndAddressCi(@Param("limit") int limit);


    @Select("""
            <script>
                SELECT *
                FROM dex_token_detail
                <where>
                    platform = #{platformId}
                    <if test="startAddress != null and startAddress != ''">
                        AND address > #{startAddress}
                    </if>
                </where>
                ORDER BY  address ASC
                LIMIT #{limit}
            </script>
            """)
    List<DexTokenDetailEntity> scanTokenPage(@Param("platformId") Integer platformId, @Param("startAddress") String startAddress, @Param("limit") int limit);


    @Update("""
                <script>
                    UPDATE dex_token_detail
                      <set>
                        <if test="volume1m != null">volume_1m = #{volume1m},</if>
                        <if test="volumeUsd1m != null">volume_usd_1m = #{volumeUsd1m},</if>
                        <if test="buyVolume1m != null">buy_volume_1m = #{buyVolume1m},</if>
                        <if test="sellVolume1m != null">sell_volume_1m = #{sellVolume1m},</if>
                        <if test="buyVolumeUsd1m != null">buy_volume_usd_1m = #{buyVolumeUsd1m},</if>
                        <if test="sellVolumeUsd1m != null">sell_volume_usd_1m = #{sellVolumeUsd1m},</if>
                        <if test="txs1m != null">txs_1m = #{txs1m},</if>
                        <if test="buyTxs1m != null">buy_txs_1m = #{buyTxs1m},</if>
                        <if test="sellTxs1m != null">sell_txs_1m = #{sellTxs1m},</if>
                        <if test="priceChange1m != null">price_change_1m = #{priceChange1m},</if>
                        <if test="traders1m != null">traders_1m = #{traders1m},</if>
                        <if test="buyTraders1m != null">buy_traders_1m = #{buyTraders1m},</if>
                        <if test="sellTraders1m != null">sell_traders_1m = #{sellTraders1m},</if>
                        <if test="volume5m != null">volume_5m = #{volume5m},</if>
                        <if test="volumeUsd5m != null">volume_usd_5m = #{volumeUsd5m},</if>
                        <if test="buyVolume5m != null">buy_volume_5m = #{buyVolume5m},</if>
                        <if test="sellVolume5m != null">sell_volume_5m = #{sellVolume5m},</if>
                        <if test="buyVolumeUsd5m != null">buy_volume_usd_5m = #{buyVolumeUsd5m},</if>
                        <if test="sellVolumeUsd5m != null">sell_volume_usd_5m = #{sellVolumeUsd5m},</if>
                        <if test="txs5m != null">txs_5m = #{txs5m},</if>
                        <if test="buyTxs5m != null">buy_txs_5m = #{buyTxs5m},</if>
                        <if test="sellTxs5m != null">sell_txs_5m = #{sellTxs5m},</if>
                        <if test="priceChange5m != null">price_change_5m = #{priceChange5m},</if>
                        <if test="traders5m != null">traders_5m = #{traders5m},</if>
                        <if test="buyTraders5m != null">buy_traders_5m = #{buyTraders5m},</if>
                        <if test="sellTraders5m != null">sell_traders_5m = #{sellTraders5m},</if>
                        <if test="volume1h != null">volume_1h = #{volume1h},</if>
                        <if test="volumeUsd1h != null">volume_usd_1h = #{volumeUsd1h},</if>
                        <if test="buyVolume1h != null">buy_volume_1h = #{buyVolume1h},</if>
                        <if test="sellVolume1h != null">sell_volume_1h = #{sellVolume1h},</if>
                        <if test="buyVolumeUsd1h != null">buy_volume_usd_1h = #{buyVolumeUsd1h},</if>
                        <if test="sellVolumeUsd1h != null">sell_volume_usd_1h = #{sellVolumeUsd1h},</if>
                        <if test="txs1h != null">txs_1h = #{txs1h},</if>
                        <if test="buyTxs1h != null">buy_txs_1h = #{buyTxs1h},</if>
                        <if test="sellTxs1h != null">sell_txs_1h = #{sellTxs1h},</if>
                        <if test="priceChange1h != null">price_change_1h = #{priceChange1h},</if>
                        <if test="traders1h != null">traders_1h = #{traders1h},</if>
                        <if test="buyTraders1h != null">buy_traders_1h = #{buyTraders1h},</if>
                        <if test="sellTraders1h != null">sell_traders_1h = #{sellTraders1h},</if>
                        <if test="volume4h != null">volume_4h = #{volume4h},</if>
                        <if test="volumeUsd4h != null">volume_usd_4h = #{volumeUsd4h},</if>
                        <if test="buyVolume4h != null">buy_volume_4h = #{buyVolume4h},</if>
                        <if test="sellVolume4h != null">sell_volume_4h = #{sellVolume4h},</if>
                        <if test="buyVolumeUsd4h != null">buy_volume_usd_4h = #{buyVolumeUsd4h},</if>
                        <if test="sellVolumeUsd4h != null">sell_volume_usd_4h = #{sellVolumeUsd4h},</if>
                        <if test="txs4h != null">txs_4h = #{txs4h},</if>
                        <if test="buyTxs4h != null">buy_txs_4h = #{buyTxs4h},</if>
                        <if test="sellTxs4h != null">sell_txs_4h = #{sellTxs4h},</if>
                        <if test="priceChange4h != null">price_change_4h = #{priceChange4h},</if>
                        <if test="traders4h != null">traders_4h = #{traders4h},</if>
                        <if test="buyTraders4h != null">buy_traders_4h = #{buyTraders4h},</if>
                        <if test="sellTraders4h != null">sell_traders_4h = #{sellTraders4h},</if>
                        <if test="volume24h != null">volume_24h = #{volume24h},</if>
                        <if test="volumeUsd24h != null">volume_usd_24h = #{volumeUsd24h},</if>
                        <if test="buyVolume24h != null">buy_volume_24h = #{buyVolume24h},</if>
                        <if test="sellVolume24h != null">sell_volume_24h = #{sellVolume24h},</if>
                        <if test="buyVolumeUsd24h != null">buy_volume_usd_24h = #{buyVolumeUsd24h},</if>
                        <if test="sellVolumeUsd24h != null">sell_volume_usd_24h = #{sellVolumeUsd24h},</if>
                        <if test="txs24h != null">txs_24h = #{txs24h},</if>
                        <if test="buyTxs24h != null">buy_txs_24h = #{buyTxs24h},</if>
                        <if test="sellTxs24h != null">sell_txs_24h = #{sellTxs24h},</if>
                        <if test="priceChange24h != null">price_change_24h = #{priceChange24h},</if>
                        <if test="traders24h != null">traders_24h = #{traders24h},</if>
                        <if test="buyTraders24h != null">buy_traders_24h = #{buyTraders24h},</if>
                        <if test="sellTraders24h != null">sell_traders_24h = #{sellTraders24h},</if>
                      </set>
                      WHERE platform = #{platform} AND address = #{address} AND ts = #{ts}
               </script>
            """)
    int updateStats(TokenDetailExtendDTO tokenDetailExtendDTO);

    @Update("""
                <script>
                    UPDATE dex_token_detail
                      SET listed_flag =
                      <choose>
                        <when test="list != null and list.size() > 0">
                          CASE
                          <foreach collection="list" item="item">
                            WHEN platform = #{item.platform} AND address = #{item.address} THEN #{item.listedFlag}
                          </foreach>
                          ELSE listed_flag
                          END
                        </when>
                        <otherwise>
                          listed_flag
                        </otherwise>
                      </choose>
                      WHERE (platform, address) IN
                      <foreach collection="list" item="item" separator="," open="(" close=")">
                        (#{item.platform}, #{item.address})
                      </foreach>
               </script>
            """)
    int batchUpdateListedFlag(@Param("list") List<DexTokenDetailEntity> list);



    @Select("""
            <script>
            SELECT * FROM dex_token_detail FORCE INDEX(idx_launched_at_address)
            WHERE platform = #{entity.platform} AND launched_at IS NOT NULL
            AND address > #{entity.lastAddress}
            ORDER BY address
            LIMIT #{entity.limit}
            </script>
            """)
    List<DexTokenDetailEntity> queryDetailForMigrateSync(@Param("entity") DexTokenDetailQueryEntity queryEntity);



    @Select("""
            <script>
            SELECT platform,address,user_address FROM dex_token_detail
            WHERE (platform, address) IN
            <foreach collection="keys" item="item" open="(" separator="," close=")">
                (#{item.platform}, #{item.address})
            </foreach>
            </script>
            """)
    List<DexTokenDetailEntity> batchGetPkField(@Param("keys") List<DexTokenDetailEntity> keys);


}
