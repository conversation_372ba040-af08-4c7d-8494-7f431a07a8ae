package com.cmc.dex.dao.entity.tidb;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import java.util.Date;
import lombok.Data;

/**
 * DexMemeMigrateDetailEntity
 * <AUTHOR> ricky.x
 * @date: 2025/6/20 10:57
 */
@Data
@TableName("dex_migrate_detail")
public class DexMigrateDetailEntity {
    /**
     * platform
     */
    @TableField("platform")
    private Integer platform;

    /**
     * address
     */
    @TableField("address")
    private String address;

    /**
     * userAddress
     */
    @TableField("user_address")
    private String userAddress;

    @TableField("publish_at")
    private Long publishAt;

    @TableField("launched_at")
    private Long launchedAt;

    /**
     * poolSource
     */
    @TableField("pool_source")
    private String poolSource;


    @TableField("db_create_time")
    private Date dbCreateTime;
    @TableField("db_modify_time")
    private Date dbModifyTime;
} 