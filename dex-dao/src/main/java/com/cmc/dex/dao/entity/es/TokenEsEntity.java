package com.cmc.dex.dao.entity.es;

import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Token ElasticSearch Entity
 *
 * <AUTHOR>
 * @since 2025/7/4
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@Document(indexName = "dex_token")
public class TokenEsEntity {

    @Id
    private String id;

    @Field(type = FieldType.Integer)
    private Integer platform;

    @Field(type = FieldType.Keyword)
    private String address;

    @Field(type = FieldType.Keyword)
    private String symbol;

    @Field(type = FieldType.Double)
    private Double volumeUsd24h;

    @Field(type = FieldType.Double)
    private Double marketcap;

    @Field(type = FieldType.Double)
    private Double liquidityUsd;
}