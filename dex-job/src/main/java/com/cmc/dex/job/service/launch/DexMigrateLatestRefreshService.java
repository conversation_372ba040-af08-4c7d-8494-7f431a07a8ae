package com.cmc.dex.job.service.launch;

import com.cmc.dex.dao.entity.tidb.DexMigrateDetailEntity;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.dao.repository.redis.DexMigrateRedisRepository;
import com.cmc.dex.dao.repository.tidb.repository.DexMigrateDetailRepository;
import com.cmc.dex.dao.repository.tidb.repository.DexTokenDetailRepository;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.StringUtils;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;


/**
 * MemeLaunchSyncService
 * <AUTHOR> ricky.x
 * @date: 2025/5/6 16:24
 */
@Slf4j
@Service
public class DexMigrateLatestRefreshService {

    @Value("${com.cmc.dquery-job.dev-migrate.latest-refresh.query-db-size:500}")
    protected Integer queryDbSize;

    @Value("${com.cmc.dquery-job.dev-migrate.latest-refresh.chains:14,16}")
    protected List<Integer> hisRefreshChains;

    @Autowired
    private DexTokenDetailRepository dexTokenDetailRepository;

    @Autowired
    private DexMigrateDetailRepository dexMigrateDetailRepository;

    @Autowired
    private DexMigrateRedisRepository devMigrateRedisRepository;



    public Mono<Boolean> process() {
        if (CollectionUtils.isEmpty(hisRefreshChains)) {
            return Mono.just(Boolean.TRUE);
        }
        return Flux.fromIterable(hisRefreshChains)
                .doOnNext(e -> log.info("DexMigrateLatestRefreshService begin chain:{}", e))
                .flatMap(this::processByChain,2)
                .collectList()
                .thenReturn(Boolean.TRUE);

    }

    private Mono<Boolean> processByChain(Integer platform){
        return queryLatestPendingData(platform)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(this::processAndSave)
                .defaultIfEmpty(Boolean.TRUE);
    }


    protected Mono<List<DexMigrateDetailEntity>> queryLatestPendingData(Integer platform) {
        long start = System.currentTimeMillis();
        return dexMigrateDetailRepository.queryMissUserCa(platform, queryDbSize)
                .switchIfEmpty(Mono.defer(() -> Mono.just(List.of())))
                .doOnNext(e -> log.info("DexMigrateLatestRefreshService process, chain:{}, size:{}, time:{}",
                        platform, e.size(), System.currentTimeMillis() - start));
    }



    protected Mono<Boolean> processAndSave(List<DexMigrateDetailEntity> entities){
        if (CollectionUtils.isEmpty(entities)) {
            return Mono.just(Boolean.TRUE);
        }
        return fillUserCa(entities)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(list -> dexMigrateDetailRepository.batchUpdateUserAddress(list)
                        .publishOn(Schedulers.boundedElastic())
                        .doOnNext(e -> {
                            devMigrateRedisRepository.removeCache(list).subscribe();
                        }))
                .defaultIfEmpty(Boolean.TRUE);
    }

    private Mono<List<DexMigrateDetailEntity>> fillUserCa(List<DexMigrateDetailEntity> migrateDetailEntities) {
        return getUserAddressMap(migrateDetailEntities)
                .map(userCaMap -> {
                    List<DexMigrateDetailEntity> containUserCa = new ArrayList<>();
                    for (DexMigrateDetailEntity item : migrateDetailEntities) {
                        String key = buildKey(item.getPlatform(), item.getAddress());
                        DexTokenDetailEntity entity = userCaMap.get(key);
                        if (entity == null || StringUtils.isBlank(entity.getUserAddress())) {
                            continue;
                        }
                        item.setUserAddress(entity.getUserAddress());
                        containUserCa.add(item);
                    }
                    return containUserCa;
                });
    }

    //查询数据
    private Mono<Map<String,DexTokenDetailEntity>> getUserAddressMap(List<DexMigrateDetailEntity> migrateDetailEntities) {
        List<DexTokenDetailEntity> keys = migrateDetailEntities.stream().map(e -> DexTokenDetailEntity.builder()
                .platform(e.getPlatform()).address(e.getAddress()).build()).toList();
        return dexTokenDetailRepository.batchGetPkField(keys)
                .map(list -> list.stream().filter(e -> e != null && StringUtils.isNotBlank(e.getUserAddress()))
                        .collect(Collectors.toList()))
                .map(e -> e.stream()
                        .collect(Collectors.toMap(
                                entity -> buildKey(entity.getPlatform(), entity.getAddress()),
                                entity -> entity,
                                (e1, e2) -> e1
                        )))
                .defaultIfEmpty(Map.of());
    }

    private String buildKey(Integer platform, String address) {
        return platform.toString().concat("_").concat(address);
    }





}
