package com.cmc.dex.job;

import com.cmc.dex.common.util.MetricsHelper;
import com.cmc.dex.job.config.PublisherProperties;
import com.cmc.framework.redis.annotation.EnableRedisConfiguration;
import com.cmc.framework.redis.annotation.EnableRedisConfiguration.RedisNamespace;
import lombok.extern.slf4j.Slf4j;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.ImportAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.web.servlet.WebMvcAutoConfiguration;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.scheduling.annotation.EnableScheduling;
import com.baomidou.dynamic.datasource.spring.boot.autoconfigure.DynamicDataSourceAutoConfiguration;

/**
 * <AUTHOR>
 */
@Slf4j
@SpringBootApplication(scanBasePackages = {"com.cmc.dex.*"}, exclude = {WebMvcAutoConfiguration.class})
@EnableConfigurationProperties(PublisherProperties.class)
@EnableAspectJAutoProxy(proxyTargetClass = true)
@MapperScan({"com.cmc.dex.dao.repository.mysql", "com.cmc.dex.dao.repository.tidb.mappers"})
@EnableScheduling
@ImportAutoConfiguration({DynamicDataSourceAutoConfiguration.class})
@EnableRedisConfiguration({@RedisNamespace("backend.redis.dexer"),
                            @RedisNamespace(value = "backend.redis.bigdata.blockchain",reactiveRedisTemplate = "bigDataRedisTemplate")})

public class DexJobApplication {

    public static void main(String[] args) {
        try {
            MetricsHelper.enableSchedulerMetrics();
            SpringApplication.run(DexJobApplication.class, args);
        } catch (Exception e) {
            log.error("job start error.", e);
        }
    }
}
