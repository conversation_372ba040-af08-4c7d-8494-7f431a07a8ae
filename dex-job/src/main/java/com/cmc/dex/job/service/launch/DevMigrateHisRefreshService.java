package com.cmc.dex.job.service.launch;

import com.cmc.dex.dao.entity.tidb.DexMigrateDetailEntity;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.dao.repository.tidb.repository.DexMigrateDetailRepository;
import com.cmc.dex.dao.repository.tidb.repository.DexTokenDetailRepository;
import com.cmc.framework.utils.CollectionUtils;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;



/**
 * MemeLaunchSyncService
 * <AUTHOR> ricky.x
 * @date: 2025/5/6 16:24
 */
@Slf4j
@Service
public class DevMigrateHisRefreshService {

    @Value("${com.cmc.dquery-job.dev-migrate.his-refresh.query-db-size:500}")
    protected Integer queryDbSize;

    @Value("${com.cmc.dquery-job.dev-migrate.his-refresh.batch-insert-size:200}")
    protected Integer insertDbSize;

    @Value("${com.cmc.dquery-job.dev-migrate.his-refresh.chains:14,16}")
    protected List<Integer> hisRefreshChains;

    @Autowired
    private DexTokenDetailRepository dexTokenDetailRepository;

    @Autowired
    private DexMigrateDetailRepository dexMigrateDetailRepository;


    public Mono<Boolean> process() {
        if (CollectionUtils.isEmpty(hisRefreshChains)) {
            return Mono.just(Boolean.TRUE);
        }
        return Flux.fromIterable(hisRefreshChains)
                .doOnNext(e -> log.info("DevMigrateHisRefreshService begin chain:{}", e))
                .flatMap(platform -> processPlatformRecursively(platform, null),2)
                .collectList()
                .thenReturn(Boolean.TRUE);
    }


    /**
     * 分页查询数据
     * 保存db
     */
    private Mono<Boolean> processPlatformRecursively(Integer platform,String lastAddress) {
        return queryPendingData(platform, lastAddress, queryDbSize)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(this::processAndSave)
                .flatMap(result -> {
                    int batchSize = result.getT1();
                    Optional<String> nextCursor = result.getT2();
                    if (nextCursor.isPresent() && batchSize >= queryDbSize) {
                        return processPlatformRecursively(platform, nextCursor.get());
                    }
                    return Mono.just(Boolean.TRUE);
                })
                .defaultIfEmpty(Boolean.TRUE);
    }

    protected Mono<List<DexTokenDetailEntity>> queryPendingData(Integer platformId,String lastAddress, int limit) {
        long start = System.currentTimeMillis();
        return dexTokenDetailRepository.queryLaunchByLaunchAt(platformId, lastAddress, limit)
                .doOnNext(e -> log.info("DevMigrateHisRefreshService process, chainId:{} lastAddress:{}, limit:{}, count:{}, time:{}",
                        platformId, lastAddress, limit, e.size(), System.currentTimeMillis() - start));
    }



    protected Mono<Tuple2<Integer, Optional<String>>> processAndSave(List<DexTokenDetailEntity> entities){
        if (CollectionUtils.isEmpty(entities)) {
            return Mono.just(Tuples.of(-1, Optional.empty()));
        }
        String lastAddress = Optional.ofNullable(entities.get(entities.size() - 1))
                .map(DexTokenDetailEntity::getAddress)
                .orElse(null);
        Tuple2<Integer, Optional<String>> result = Tuples.of(entities.size(), Optional.ofNullable(lastAddress));
        List<DexMigrateDetailEntity> migrateDetailEntities = entities.stream()
                .map(this::buildDetailEntity)
                .toList();
        return saveFilterDb(migrateDetailEntities)
                .thenReturn(result);
    }

    private DexMigrateDetailEntity buildDetailEntity(DexTokenDetailEntity source) {
        DexMigrateDetailEntity target = new DexMigrateDetailEntity();
        target.setPlatform(source.getPlatform());
        target.setAddress(source.getAddress());
        target.setUserAddress(source.getUserAddress());
        target.setPublishAt(transData(source.getPublishAt()));
        target.setLaunchedAt(transData(source.getLaunchedAt()));
        target.setPoolSource(source.getPoolSource());
        return target;
    }


    private Mono<Long> saveFilterDb(List<DexMigrateDetailEntity> dexTrendingEntities) {
        if (CollectionUtils.isEmpty(dexTrendingEntities)) {
            return Mono.just(0L);
        }
        return Flux.fromIterable(dexTrendingEntities)
                .buffer(insertDbSize)
                .flatMap(bufferList -> dexMigrateDetailRepository.batchInsert(bufferList))
                .count();
    }


    private Long transData(Date source){
        return source == null ? null : source.getTime();
    }
}
