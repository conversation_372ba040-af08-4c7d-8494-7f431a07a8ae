package com.cmc.dex.job.handler.pool;

import static com.cmc.dex.common.constants.MetricConstants.CMC_DQUERY_JOB_HANDLER_ERRORS;
import static com.cmc.dex.common.constants.MetricConstants.TAG_JOB_NAME;

import com.cmc.dex.dao.entity.tidb.DexPoolDetailEntity;
import com.cmc.dex.dao.repository.tidb.repository.DexPoolDetailRepository;
import com.cmc.dex.job.service.DexPoolMigrateService;
import com.cmc.framework.job.BaseJobHandler;
import com.cmc.framework.job.JobHandlingResult;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.StringUtils;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * MemeUserCaSyncHandler
 * <AUTHOR> ricky.x
 * @date: 2025/6/23 16:30
 */
@Component
@Slf4j
public class DexPoolLatestRefreshHandler extends BaseJobHandler<String, String> {

    @Autowired
    private DexPoolDetailRepository dexPoolDetailRepository;

    @Autowired
    private DexPoolMigrateService dexPoolMigrateService;

    @Value("${com.cmc.dquery-job.pool.latest-refresh.query-db-size:500}")
    protected Integer queryDbSize;

    @Autowired
    private CmcMeterRegistry cmcMeterRegistry;

    @SneakyThrows
    @Override
    public JobHandlingResult<String> doInHandle(String args) {
        try {
            queryLatestPendingData()
                    .filter(CollectionUtils::isNotEmpty)
                    .flatMap(e -> dexPoolMigrateService.resetMemeProtocolLiq(e))
                    .defaultIfEmpty(Boolean.TRUE)
                    .toFuture().get();
        } catch (Exception e) {
            cmcMeterRegistry.counter(CMC_DQUERY_JOB_HANDLER_ERRORS,TAG_JOB_NAME,"DexPoolLatestRefreshHandler");
            log.error("DexPoolLatestRefreshHandler failed, error:", e);
            return new JobHandlingResult<>(false, e.getMessage());
        }
        return new JobHandlingResult<>(true, StringUtils.EMPTY);
    }


    private Mono<List<DexPoolDetailEntity>> queryLatestPendingData() {
        long start = System.currentTimeMillis();
        return dexPoolDetailRepository.queryLatestLaunched(queryDbSize)
                .switchIfEmpty(Mono.defer(() -> Mono.just(List.of())))
                .doOnNext(e -> log.info("DexPoolLatestRefreshHandler process, size:{}, time:{}",
                        e.size(), System.currentTimeMillis() - start));
    }

}