package com.cmc.dex.job.handler;

import lombok.extern.slf4j.Slf4j;
import com.cmc.dex.job.handler.pool.DexPoolHisRefreshHandler;
import com.cmc.dex.job.handler.pool.DexPoolLatestRefreshHandler;
import com.cmc.dex.job.handler.meme.DevMigrateHisRefreshHandler;
import com.cmc.dex.job.handler.meme.DexMigrateLatestRefreshHandler;
import com.cmc.dex.job.handler.trending.TokenTrendingFilterHandler;
import com.cmc.dex.job.handler.trending.TokenTrendingScoreHandler;
import com.cmc.framework.job.JobHandlingResult;
import com.cmc.framework.utils.JacksonUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.annotation.XxlJob;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR> Wang
 */
@Component
@Slf4j
public class XxlJobHandler {

  /*  @XxlJob("dexPoolDataMigrationHandler")
    public ReturnT<String> dexPoolDataMigrationHandler(String params, DexPoolMigrationJobHandler dexPoolMigrationJobHandler) {
        JobHandlingResult<String> result = dexPoolMigrationJobHandler.handle("");
        return handleResult(result);
    }
    @XxlJob("dexTokenDataMigrationHandler")
    public ReturnT<String> dexTokenDataMigrationHandler(String params,DexTokenMigrationJobHandler dexTokenMigrationJobHandler) {
        JobHandlingResult<String> result = dexTokenMigrationJobHandler.handle("");
        return handleResult(result);
    }
    @XxlJob("dexTokenSummaryDataMigrationHandler")
    public ReturnT<String> dexTokenSummaryDataMigrationHandler(String params,DexTokenSummaryMigrationJobHandler dexTokenSummaryMigrationJobHandler) {
        JobHandlingResult<String> result = dexTokenSummaryMigrationJobHandler.handle("");
        return handleResult(result);
    }
    @XxlJob("dexPoolSummaryDataMigrationHandler")
    public ReturnT<String> dexPoolSummaryDataMigrationHandler(String params,DexPoolSummaryMigrationJobHandler dexPoolSummaryMigrationJobHandler) {
        JobHandlingResult<String> result = dexPoolSummaryMigrationJobHandler.handle("");
        return handleResult(result);
    }*/

    private ReturnT<String> handleResult(JobHandlingResult<?> result) {
        if (result.isSucceeded()) {
            return new ReturnT<>(ReturnT.SUCCESS_CODE, JacksonUtils.serialize(result.getData()));
        } else {
            return new ReturnT<>(ReturnT.FAIL_CODE, result.getErrorMsg());
        }
    }

    @XxlJob("klineDataMigrationJobHandler")
    public ReturnT<String> klineDataMigrationJobHandler(String params,
        KlineDataMigrationHandler klineDataMigrationHandler) {
        JobHandlingResult<String> result = klineDataMigrationHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("klineDataAggregationJobHandler")
    public ReturnT<String> klineDataAggregationJobHandler(String params,
        KlineDataAggregationHandler klineDataAggregationHandler) {
        JobHandlingResult<String> result = klineDataAggregationHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("profitTopTraderProcessHandler")
    public ReturnT<String> profitTopTraderProcessHandler(String params,
                                                         ProfitTopTraderProcessHandler profitTopTraderProcessHandler) {
        JobHandlingResult<String> result = profitTopTraderProcessHandler.handle(params);
        return handleResult(result);
    }


    @XxlJob("dexGainerHandler")
    public ReturnT<String> dexGainerHandler(String params,
        DexGainerHandler dexGainerHandler) {
        JobHandlingResult<String> result = dexGainerHandler.handle(params);
        return handleResult(result);
    }


    @XxlJob("tokenTrendingFilterHandler")
    public ReturnT<String> profitTopTraderProcessHandler(String params,
                                                         TokenTrendingFilterHandler tokenTrendingFilterHandler) {
        JobHandlingResult<String> result = tokenTrendingFilterHandler.handle(params);
        return handleResult(result);
    }


    @XxlJob("tokenTrendingScoreHandler")
    public ReturnT<String> profitTopTraderProcessHandler(String params,
                                                         TokenTrendingScoreHandler tokenTrendingScoreHandler) {
        JobHandlingResult<String> result = tokenTrendingScoreHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("syncNewTokenCacheJobHandler")
    public ReturnT<String> syncNewTokenCacheJobHandler(String params, SyncNewTokenCacheJobHandler syncNewTokenCacheJobHandler) {
        JobHandlingResult<String> result = syncNewTokenCacheJobHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("tokenSecurityDataJobHandler")
    public ReturnT<String> tokenSecurityDataJobHandler(String params, TokenSecurityDataJobHandler tokenSecurityDataJobHandler) {
        JobHandlingResult<String> result = tokenSecurityDataJobHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("hotWordsTrimJobHandler")
    public ReturnT<String> hotWordsTrimJobHandler(String params, HotWordsTrimJobHandler hotWordsTrimJobHandler) {
        JobHandlingResult<String> result = hotWordsTrimJobHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("batchUpdateSymbolAddressCiJobHandler")
    public ReturnT<String> batchUpdateSymbolAddressCiJobHandler(String params, BatchUpdateSymbolAddressCiJobHandler batchUpdateSymbolAddressCiJobHandler) {
        JobHandlingResult<String> result = batchUpdateSymbolAddressCiJobHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("dexTokenKlineMigrateJobHandler")
    public ReturnT<String> dexTokenKlineMigrateJobHandler(String params, DexTokenKlineMigrateJobHandler dexTokenKlineMigrateJobHandler) {
        JobHandlingResult<String> result = dexTokenKlineMigrateJobHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("tokenSecurityRealTimeRefreshHandler")
    public ReturnT<String> tokenSecurityRealTimeRefreshHandler(String params, TokenSecurityRealTimeRefreshHandler tokenSecurityRealTimeRefreshHandler) {
        JobHandlingResult<String> result = tokenSecurityRealTimeRefreshHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("listedTokenRefreshHandler")
    public ReturnT<String> listedTokenRefreshHandler(String params, ListedTokenRefreshHandler listedTokenRefreshHandler) {
        JobHandlingResult<String> result = listedTokenRefreshHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("tokenPublishAtCompensationDataJobHandler")
    public ReturnT<String> tokenPublishAtCompensationDataJobHandler(String params, TokenPublishAtCompensationDataJobHandler tokenPublishAtCompensationDataJobHandler) {
        JobHandlingResult<String> result = tokenPublishAtCompensationDataJobHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("memeExploreHandler")
    public ReturnT<String> tokenPublishAtCompensationDataJobHandler(String params, MemeExploreHandler memeExploreHandler) {
        JobHandlingResult<String> result = memeExploreHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("dexNewTokenLocalCacheHandler")
    public ReturnT<String> dexNewTokenLocalCacheHandler(String params, DexNewTokenLocalCacheHandler dexNewTokenLocalCacheHandler) {
        JobHandlingResult<String> result = dexNewTokenLocalCacheHandler.handle(params);
        return handleResult(result);
    }


    @XxlJob("devMigrateHisRefreshHandler")
    public ReturnT<String> devMigrateHisRefreshHandler(String params, DevMigrateHisRefreshHandler devMigrateHisRefreshHandler) {
        JobHandlingResult<String> result = devMigrateHisRefreshHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("dexMigrateLatestRefreshHandler")
    public ReturnT<String> dexMigrateLatestRefreshHandler(String params, DexMigrateLatestRefreshHandler dexMigrateLatestRefreshHandler) {
        JobHandlingResult<String> result = dexMigrateLatestRefreshHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("poolMapRefreshHandler")
    public ReturnT<String> poolMapRefreshHandler(String params, PoolMapRefreshHandler poolMapRefreshHandler) {
        JobHandlingResult<String> result = poolMapRefreshHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("dexPoolHisRefreshHandler")
    public ReturnT<String> dexPoolHisRefreshHandler(String params, DexPoolHisRefreshHandler dexPoolHisRefreshHandler) {
        JobHandlingResult<String> result = dexPoolHisRefreshHandler.handle(params);
        return handleResult(result);
    }

    @XxlJob("dexPoolLatestRefreshHandler")
    public ReturnT<String> dexPoolLatestRefreshHandler(String params, DexPoolLatestRefreshHandler dexPoolLatestRefreshHandler) {
        JobHandlingResult<String> result = dexPoolLatestRefreshHandler.handle(params);
        return handleResult(result);
    }



}
