package com.cmc.dex.job.consumer;

import com.cmc.dex.job.consumer.handler.MemeTokenMigrateProcessor;
import com.cmc.dex.model.kafka.PoolTokenPairsDTO;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;


/**
 * MemeTokenMigrateConsumer
 * <AUTHOR> ricky.x
 * @date: 2025/5/15 16:56
 */
@Component
@Slf4j
public class MemeTokenMigrateSolConsumer extends AbstractManualCommitConsumer<PoolTokenPairsDTO>{

    @Value("${com.cmc.dquery-job.comsumer.meme-migrate-switch-sol:false}")
    private boolean migrateSwitch;

    @Autowired
    private MemeTokenMigrateProcessor memeTokenMigrateProcessor;

    @Override
    protected boolean process(List<PoolTokenPairsDTO> records) {
        if(!migrateSwitch){
            return true;
        }
        List<PoolTokenPairsDTO> validateList = records.stream()
                .filter(e -> e.getLaunchAt() != null)
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(validateList)) {
            return true;
        }
        try {
            memeTokenMigrateProcessor.batchHandle(validateList).block();
            return true;
        } catch (Exception e) {
            log.error("MemeTokenMigrateSolConsumer failed,wait retry, entities: {}", JacksonUtils.serialize(validateList), e);
            return false;
        }
    }

}
