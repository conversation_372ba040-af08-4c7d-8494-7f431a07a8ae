package com.cmc.dex.job.handler.pool;

import static com.cmc.dex.common.constants.MetricConstants.CMC_DQUERY_JOB_HANDLER_ERRORS;
import static com.cmc.dex.common.constants.MetricConstants.TAG_JOB_NAME;

import com.cmc.dex.dao.entity.tidb.DexPoolDetailEntity;
import com.cmc.dex.dao.repository.tidb.repository.DexPoolDetailRepository;
import com.cmc.dex.job.service.DexPoolMigrateService;
import com.cmc.framework.job.BaseJobHandler;
import com.cmc.framework.job.JobHandlingResult;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.StringUtils;
import java.util.Date;
import java.util.List;
import java.util.Optional;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import reactor.util.function.Tuples;


/**
 * TopTradeSyncProdHandler
 * <AUTHOR> ricky.x
 * @date: 2025/5/21 15:54
 */
@Component
@Slf4j
public class DexPoolHisRefreshHandler extends BaseJobHandler<String, String> {

    @Value("${com.cmc.dquery-job.meme-pool.his-refresh.db-size:500}")
    protected Integer queryDbSize;

    @Value("${com.cmc.dquery-job.meme-pool.his-refresh.start-time:1687699483000}")
    protected Long queryStartTime;


    @Autowired
    private DexPoolDetailRepository dexPoolDetailRepository;

    @Autowired
    private DexPoolMigrateService dexPoolMigrateService;

    @Autowired
    private CmcMeterRegistry cmcMeterRegistry;


    @SneakyThrows
    @Override
    public JobHandlingResult<String> doInHandle(String args) {
        try {
            Date start = new Date(queryStartTime);
            processPlatformRecursively(start).toFuture().get();
        } catch (Exception e) {
            cmcMeterRegistry.counter(CMC_DQUERY_JOB_HANDLER_ERRORS,TAG_JOB_NAME,"DexPoolHisRefreshHandler");
            log.error("DevMigrateHisRefreshHandler failed, error:", e);
            return new JobHandlingResult<>(false, e.getMessage());
        }
        return new JobHandlingResult<>(true, StringUtils.EMPTY);
    }

    /**
     * 分页查询数据
     * 保存db
     */
    private Mono<Boolean> processPlatformRecursively(Date launchedAt) {
        return queryPendingData(queryDbSize, launchedAt)
                .filter(CollectionUtils::isNotEmpty)
                .flatMap(this::processAndSave)
                .flatMap(result -> {
                    int batchSize = result.getT1();
                    Optional<Date> nextCursor = result.getT2();
                    if (nextCursor.isPresent() && batchSize >= queryDbSize) {
                        return processPlatformRecursively(nextCursor.get());
                    }
                    return Mono.just(Boolean.TRUE);
                })
                .defaultIfEmpty(Boolean.TRUE);
    }

    protected Mono<List<DexPoolDetailEntity>> queryPendingData(int limit, Date launchedAt) {
        long start = System.currentTimeMillis();
        return dexPoolDetailRepository.queryLaunchedByPage(limit, launchedAt)
                .doOnNext(e -> log.info("DexPoolMigrateServiceImpl queryPendingData launchedAt:{}, limit:{} count:{} time:{}",
                        launchedAt, limit, e.size(), System.currentTimeMillis() - start));
    }

    protected Mono<Tuple2<Integer, Optional<Date>>> processAndSave(List<DexPoolDetailEntity> entities) {
        if (CollectionUtils.isEmpty(entities)) {
            return Mono.just(Tuples.of(-1, Optional.empty()));
        }
        Date lastLaunchedAt = Optional.ofNullable(entities.get(entities.size() - 1))
                .map(DexPoolDetailEntity::getLaunchedAt)
                .orElse(null);
        Tuple2<Integer, Optional<Date>> result = Tuples.of(entities.size(), Optional.ofNullable(lastLaunchedAt));
        return dexPoolMigrateService.resetMemeProtocolLiq(entities)
                .thenReturn(result);
    }


}
