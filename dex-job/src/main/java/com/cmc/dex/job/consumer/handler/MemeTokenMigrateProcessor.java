package com.cmc.dex.job.consumer.handler;

import com.cmc.dex.business.service.token.TokenMatchService;
import com.cmc.dex.dao.entity.tidb.DexMigrateDetailEntity;
import com.cmc.dex.dao.repository.tidb.repository.DexMigrateDetailRepository;
import com.cmc.dex.model.kafka.PoolTokenPairsDTO;
import com.cmc.dex.model.kafka.TokenInfoDTO;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.ObjectUtils;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;


/**
 * DexTopTraderProcessor
 *
 * <AUTHOR> ricky.x
 * @date: 2025/4/27 16:24
 */
@Component
@Slf4j
public class MemeTokenMigrateProcessor {

    @Autowired
    private DexMigrateDetailRepository dexMigrateDetailRepository;

    @Autowired
    private TokenMatchService tokenMatchService;

    //写入db,等待job处理,处理成功则更新状态 处理失败则更新状态
    public Mono<Boolean> batchHandle(List<PoolTokenPairsDTO> validateList) {
        for (PoolTokenPairsDTO poolTokenPairs : validateList) {
            log.info("meme migrate event, platform:{} pair:{} info:{}", poolTokenPairs.getPlatformId(), poolTokenPairs.getAddress(), JacksonUtils.serialize(poolTokenPairs));
        }
        List<DexMigrateDetailEntity> list = validateList.stream().map(this::buildMigrateDetail)
                .filter(Objects::nonNull)
                .collect(Collectors.toList());
        return dexMigrateDetailRepository.batchInsert(list)
                .thenReturn(Boolean.TRUE);
    }




    private DexMigrateDetailEntity buildMigrateDetail(PoolTokenPairsDTO poolTokenPair) {
        TokenInfoDTO baseToken = getBaseTokenInfo(poolTokenPair);
        if (baseToken == null) {
            return null;
        }
        DexMigrateDetailEntity target = new DexMigrateDetailEntity();
        target.setPlatform(poolTokenPair.getPlatformId());
        target.setAddress(baseToken.getAddress());
        target.setLaunchedAt(poolTokenPair.getLaunchAt() != null
                ? poolTokenPair.getLaunchAt().getTime() : null);
        target.setPoolSource(poolTokenPair.getPoolSource());
        return target;
    }


    private TokenInfoDTO getBaseTokenInfo(PoolTokenPairsDTO poolTokenPair) {
        Integer platform = poolTokenPair.getPlatformId();
        TokenInfoDTO token0 = poolTokenPair.getToken0();
        TokenInfoDTO token1 = poolTokenPair.getToken1();
        if (ObjectUtils.isAnyNull(token0, token1) || StringUtils.isAnyBlank(token0.getAddress(), token1.getAddress())) {
            log.error("MemeTokenMigrate failed, invalid message,poolTokenPair:{} ", JacksonUtils.serialize(poolTokenPair));
            return null;
        }
        boolean result = tokenMatchService.token0AsQuote(platform, token0.getAddress(), token1.getAddress());
        log.info("MemeTokenMigrate token0AsQuote, token0:{}, token1:{} result:{}", token0.getAddress(), token1.getAddress(), result);
        return result ? token1 : token0;
    }

}
