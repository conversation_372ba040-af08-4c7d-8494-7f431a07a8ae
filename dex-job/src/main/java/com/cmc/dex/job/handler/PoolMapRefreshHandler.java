package com.cmc.dex.job.handler;

import com.cmc.dex.business.client.CmcDexServiceClient;
import com.cmc.dex.business.client.model.PairInfoDTO;
import com.cmc.dex.business.client.model.PairInfoRequestDTO;
import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.dex.dao.entity.mongodb.pool.PoolMapEntity;
import com.cmc.dex.dao.entity.tidb.DexPoolDetailEntity;
import com.cmc.dex.dao.repository.mongodb.dexer.PoolMapRepository;
import com.cmc.dex.dao.repository.tidb.mappers.DexPoolDetailMapper;
import com.cmc.dex.model.constant.StringPool;
import com.cmc.framework.job.BaseJobHandler;
import com.cmc.framework.job.JobHandlingResult;
import com.cmc.framework.utils.CollectionUtils;
import java.util.ArrayList;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class PoolMapRefreshHandler extends BaseJobHandler<String, String> {
    @Autowired
    private CmcDexServiceClient cmcDexServiceClient;
    @Autowired
    private DexPoolDetailMapper dexPoolDetailMapper;
    @Autowired
    private PoolMapRepository poolMapRepository;
    @Value("${com.cmc.dex.job.handler.pool.map.query.concurrency:10}")
    private int queryConcurrency;
    @Value("${com.cmc.dex.job.handler.pool.map.pool.batch.size:200}")
    private int poolBatchSize;
    @Value("${com.cmc.dex.job.handler.pool.map.job.switch:true}")
    private Boolean jobSwitch;
    @Value("${com.cmc.dex.job.handler.pool.map.save.batch.size:100}")
    private int saveBatchSize;
    @Value("${com.cmc.dex.job.handler.pool.map.query.batch.size:20}")
    private int queryBatchSize;

    @Override
    public JobHandlingResult<String> doInHandle(String s) {
        log.info("Start of execute PoolMapRefreshHandler");
        String lastPk = s;
        Integer lastPlatformId = Integer.valueOf(lastPk.split(StringPool.DASHED)[0]);
        String lastAddress = lastPk.split(StringPool.DASHED)[1];
        List<Integer> platformIdList = dexPoolDetailMapper.queryPlatformIds();
        if (CollectionUtils.isEmpty(platformIdList)) {
            log.info("No platformIds found");
            return new JobHandlingResult<>(true, "success");
        }
        platformIdList.sort(Integer::compareTo);
        for (Integer platformId : platformIdList) {
            if (platformId < lastPlatformId) {
                continue;
            }
            List<DexPoolDetailEntity> poolList =
                dexPoolDetailMapper.queryScroll(platformId, lastAddress, poolBatchSize);
            while (CollectionUtils.isNotEmpty(poolList)) {
                if (!jobSwitch) {
                    log.info("Pool map job stopped");
                    break;
                }
                doPoolMap(poolList);
                lastAddress = poolList.get(poolList.size() - 1).getAddress();
                lastPk = platformId + StringPool.DASHED + lastAddress;
                log.info("Last pool map pk: {}", lastPk);
                poolList = dexPoolDetailMapper.queryScroll(platformId, lastAddress, poolBatchSize);
            }
            lastAddress = "0";
        }

        log.info("End of execute PoolMapRefreshHandler");
        return new JobHandlingResult<>(true, "success");
    }

    private void doPoolMap(List<DexPoolDetailEntity> list) {
        if (CollectionUtils.isEmpty(list)) {
            return;
        }
        Flux.fromIterable(list)
            .publishOn(DexSchedulers.business())
            .map(poolDetailEntity -> PairInfoRequestDTO.builder()
                .platformId(poolDetailEntity.getPlatform())
                .pairContractAddress(poolDetailEntity.getAddress())
                .build())
            .buffer(queryBatchSize)
            .flatMap(poolInfoRequestList -> cmcDexServiceClient.getPairInfo(poolInfoRequestList)
                .subscribeOn(DexSchedulers.business()), queryConcurrency)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(Flux::fromIterable)
            .buffer(saveBatchSize)
            .map(pairList -> {
                List<PoolMapEntity> poolMapEntities = convert(pairList);
                if (CollectionUtils.isNotEmpty(poolMapEntities)) {
                    poolMapRepository.batchInsertOrUpdate(poolMapEntities, true).block();
                }
                return true;
            })
            .collectList()
            .block();

    }

    private List<PoolMapEntity> convert(List<PairInfoDTO> pairList) {
        if (CollectionUtils.isEmpty(pairList)) {
            return new ArrayList<>();
        }
        List<PoolMapEntity> poolMapEntities = new ArrayList<>();
        for (PairInfoDTO pairInfoDTO : pairList) {
            PoolMapEntity poolMapEntity = new PoolMapEntity();
            poolMapEntity.setId(pairInfoDTO.getPoolId());
            poolMapEntity.setAddress(pairInfoDTO.getPairContractAddress());
            poolMapEntity.setPlatformId(pairInfoDTO.getPlatformId());
            poolMapEntities.add(poolMapEntity);
        }
        return poolMapEntities;
    }
}
