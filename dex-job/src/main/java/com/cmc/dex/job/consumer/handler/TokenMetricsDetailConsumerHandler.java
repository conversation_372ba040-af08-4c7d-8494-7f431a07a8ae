package com.cmc.dex.job.consumer.handler;

import com.cmc.dex.business.service.token.TokenService;
import com.cmc.dex.dao.entity.redis.token.TokenStatsVO;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.job.dto.Stats;
import com.cmc.dex.job.dto.TokenRollingMetricAggregationDto;
import com.cmc.dex.job.dto.TokenRollingMetricDto;
import com.cmc.dex.job.message.MessageSenderService;
import com.cmc.dex.job.utils.BigDecimalFormatUtil;
import com.cmc.dex.model.constant.StringPool;
import com.cmc.dex.model.enums.EventTypeEnum;
import com.cmc.dex.model.enums.WebsocketBusinessTopicEnum;
import com.cmc.dex.model.kafka.TokenMetricAggregationDto;
import com.cmc.dex.model.kafka.TokenMetricDTO;
import com.cmc.dex.model.kafka.WebsocketCustomEventDTO;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.StringUtils;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Function;

/**
 * <AUTHOR>
 *
 *         This class is responsible for handling token metrics detail data, including converting DTOs, batch updating
 *         data, caching data, and sending Kafka messages.
 */
@Component
@Slf4j
public class TokenMetricsDetailConsumerHandler {
    @Autowired
    private MessageSenderService messageSenderService;
    @Autowired
    private TokenService tokenService;

    /**
     * Handle the list of TokenRollingMetricAggregationDto. Convert DTOs to data entities, send Kafka messages, and
     * batch update data asynchronously.
     *
     * @param tokenRollingMetricAggregationDtoList
     *         The list of TokenRollingMetricAggregationDto to be handled.
     */
    public void handle(List<TokenRollingMetricAggregationDto> tokenRollingMetricAggregationDtoList) {
        // Convert to token detail objects
        if (CollectionUtils.isEmpty(tokenRollingMetricAggregationDtoList)) {
            log.info("The input list to be processed is empty, no action will be taken for now.");
            return;
        }

        List<DexTokenDetailEntity> dexTokenDetailEntities =
                convertDtoToDataEntity(tokenRollingMetricAggregationDtoList);
        List<TokenMetricAggregationDto> tokenMetricAggregationDtos =
                convertTokenMetricAggregationDTO(tokenRollingMetricAggregationDtoList);

        // Send Kafka messages in real-time
        Mono<Void> sendKafkaMessageMono = Mono.empty();
        if (tokenMetricAggregationDtos != null) {
            sendKafkaMessageMono = Flux.fromIterable(tokenMetricAggregationDtos).flatMap(tokenMetricAggregationDto -> {
                WebsocketCustomEventDTO<TokenMetricAggregationDto> customEventDTO =
                        buildWebsocketCustomEventDTO(tokenMetricAggregationDto);
                return messageSenderService.sendMessage(customEventDTO).doOnSuccess(senderResult -> {
                    log.info("Message sent successfully, partition: " + senderResult.recordMetadata()
                            .partition() + ", offset: " + senderResult.recordMetadata().offset());
                }).doOnError(throwable -> {
                    log.error("Message sending failed: " + throwable.getMessage());
                });
            }, 10).onErrorResume(e -> {
                log.error("An error occurred while sending Kafka messages", e);
                return Mono.empty();
            }).then();
        }
        Mono<Void> sendPoolTokenDetailDto = tokenService.sendDexTokenDetailEvent(dexTokenDetailEntities, EventTypeEnum.UPDATE).then();
        Mono.when(sendKafkaMessageMono, sendPoolTokenDetailDto)
                .subscribe(error -> log.error("Batch cache or Kafka message sending failed: {}", error));
    }

    /**
     * Convert the list of TokenRollingMetricAggregationDto to the list of DexTokenDetailEntity.
     *
     * @param tokenRollingMetricAggregationDtoList
     *         The list of TokenRollingMetricAggregationDto to be converted.
     * @return The list of DexTokenDetailEntity.
     */
    private List<DexTokenDetailEntity> convertDtoToDataEntity(
            List<TokenRollingMetricAggregationDto> tokenRollingMetricAggregationDtoList) {
        List<DexTokenDetailEntity> dexTokenDetailEntityList = new ArrayList<>();
        Map<String, Function<TokenRollingMetricAggregationDto, TokenRollingMetricDto>> dtoGetters = new HashMap<>();

        dtoGetters.put("1m", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto1m() : null;
        });
        dtoGetters.put("5m", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto5m() : null;
        });
        dtoGetters.put("1h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto1h() : null;
        });
        dtoGetters.put("4h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto4h() : null;
        });
        dtoGetters.put("24h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto24h() : null;
        });
        dtoGetters.put("7d", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto7d() : null;
        });

        for (TokenRollingMetricAggregationDto dto : tokenRollingMetricAggregationDtoList) {
            DexTokenDetailEntity.DexTokenDetailEntityBuilder builder = DexTokenDetailEntity.builder();
            builder.address(dto.getAddress()).platform(dto.getPlatform());

            if (isValidForDecimal38_18(dto.getMarketCap())) {
                builder.marketcap(dto.getMarketCap());
            }

            builder.priceHigh(dto.getStats().getTokenRollingMetricDto24h().getTokenPriceHigh() == null ? null
                            : dto.getStats().getTokenRollingMetricDto24h().getTokenPriceHigh().toPlainString()).priceLow(
                            dto.getStats().getTokenRollingMetricDto24h().getTokenPriceLow() == null ? null
                                    : dto.getStats().getTokenRollingMetricDto24h().getTokenPriceLow().toPlainString());
            dtoGetters.forEach((time, getter) -> {
                TokenRollingMetricDto rollingDto = getter.apply(dto);
                if (rollingDto != null) {
                    updateBuilderWithRollingDto(builder, rollingDto, time);
                }
            });
            dexTokenDetailEntityList.add(builder.build());
        }
        return CollectionUtils.isEmpty(dexTokenDetailEntityList) ? null : dexTokenDetailEntityList;
    }

    /**
     * Check if the BigDecimal value is valid for DECIMAL(38, 18) type.
     *
     * @param value
     *         The BigDecimal value to be checked.
     * @return true if valid, false otherwise.
     */
    public boolean isValidForDecimal38_18(BigDecimal value) {
        if (value == null) {
            return true;
        }
        int integerPartLength = value.toBigInteger().toString().length();
        String decimalPart = value.stripTrailingZeros().toPlainString();
        int decimalIndex = decimalPart.indexOf('.');
        int decimalPartLength = decimalIndex == -1 ? 0 : decimalPart.length() - decimalIndex - 1;

        return integerPartLength <= 20 && decimalPartLength <= 18;
    }

    /**
     * Update the DexTokenDetailEntity builder with the information from TokenRollingMetricDto.
     *
     * @param builder
     *         The DexTokenDetailEntity builder to be updated.
     * @param rollingDto
     *         The TokenRollingMetricDto containing the information.
     * @param time
     *         The time period, such as "5m", "1h", etc.
     */
    private void updateBuilderWithRollingDto(DexTokenDetailEntity.DexTokenDetailEntityBuilder builder,
            TokenRollingMetricDto rollingDto, String time) {
        switch (time) {
            case "1m":
                builder.buyVolume1m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolume(),38,18))
                        .sellVolume1m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolume(),38,18))
                        .buyVolumeUsd1m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolumeUsd(),38,18))
                        .sellVolumeUsd1m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolumeUsd(),38,18))
                        .volume1m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolume(),38,18))
                        .volumeUsd1m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolumeUsd(),38,18))
                        .txs1m(rollingDto.getNumTx())
                        .buyTxs1m(rollingDto.getNumBuy())
                        .sellTxs1m(rollingDto.getNumSell())
                        .traders1m(rollingDto.getUniqueTraders())
                        .buyTraders1m(rollingDto.getBuyUniqueTraders())
                        .sellTraders1m(rollingDto.getSellUniqueTraders())
                        .priceChange1m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenPriceChangePercent(),38,18));
//                        .liquidityUsd(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getLiquidityUsd(),16,6));
                break;
            case "5m":
                builder.buyVolume5m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolume(),38,18))
                        .sellVolume5m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolume(),38,18))
                        .buyVolumeUsd5m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolumeUsd(),38,18))
                        .sellVolumeUsd5m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolumeUsd(),38,18))
                        .volume5m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolume(),38,18))
                        .volumeUsd5m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolumeUsd(),38,18))
                        .txs5m(rollingDto.getNumTx())
                        .buyTxs5m(rollingDto.getNumBuy())
                        .sellTxs5m(rollingDto.getNumSell())
                        .traders5m(rollingDto.getUniqueTraders())
                        .buyTraders5m(rollingDto.getBuyUniqueTraders())
                        .sellTraders5m(rollingDto.getSellUniqueTraders())
                        .priceChange5m(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenPriceChangePercent(),16,6));
                break;
            case "1h":
                builder.buyVolume1h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolume(),38,18))
                        .sellVolume1h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolume(),38,18))
                        .buyVolumeUsd1h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolumeUsd(),38,18))
                        .sellVolumeUsd1h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolumeUsd(),38,18))
                        .volume1h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolume(),38,18))
                        .volumeUsd1h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolumeUsd(),38,18))
                        .txs1h(rollingDto.getNumTx())
                        .buyTxs1h(rollingDto.getNumBuy())
                        .sellTxs1h(rollingDto.getNumSell())
                        .traders1h(rollingDto.getUniqueTraders())
                        .buyTraders1h(rollingDto.getBuyUniqueTraders())
                        .sellTraders1h(rollingDto.getSellUniqueTraders())
                        .priceChange1h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenPriceChangePercent(),16,6));
                break;
            case "4h":
                builder.buyVolume4h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolume(),38,18))
                        .sellVolume4h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolume(),38,18))
                        .buyVolumeUsd4h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolumeUsd(),38,18))
                        .sellVolumeUsd4h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolumeUsd(),38,18))
                        .volume4h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolume(),38,18))
                        .volumeUsd4h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolumeUsd(),38,18))
                        .txs4h(rollingDto.getNumTx())
                        .buyTxs4h(rollingDto.getNumBuy())
                        .sellTxs4h(rollingDto.getNumSell())
                        .traders4h(rollingDto.getUniqueTraders())
                        .buyTraders4h(rollingDto.getBuyUniqueTraders())
                        .sellTraders4h(rollingDto.getSellUniqueTraders())
                        .priceChange4h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenPriceChangePercent(),16,6));
                break;
            case "24h":
                builder.buyVolume24h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolume(),38,18))
                        .sellVolume24h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolume(),38,18))
                        .buyVolumeUsd24h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolumeUsd(),38,18))
                        .sellVolumeUsd24h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolumeUsd(),38,18))
                        .volume24h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolume(),38,18))
                        .volumeUsd24h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolumeUsd(),38,18))
                        .txs24h(rollingDto.getNumTx())
                        .buyTxs24h(rollingDto.getNumBuy())
                        .sellTxs24h(rollingDto.getNumSell())
                        .traders24h(rollingDto.getUniqueTraders())
                        .buyTraders24h(rollingDto.getBuyUniqueTraders())
                        .sellTraders24h(rollingDto.getSellUniqueTraders())
                        .priceChange24h(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenPriceChangePercent(),16,6));
                break;
            case "7d":
                builder.buyVolume7d(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolume(),38,18))
                        .sellVolume7d(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolume(),38,18))
                        .buyVolumeUsd7d(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getBuyVolumeUsd(),38,18))
                        .sellVolumeUsd7d(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getSellVolumeUsd(),38,18))
                        .volume7d(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolume(),38,18))
                        .volumeUsd7d(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenVolumeUsd(),38,18))
                        .txs7d(rollingDto.getNumTx())
                        .buyTxs7d(rollingDto.getNumBuy())
                        .sellTxs7d(rollingDto.getNumSell())
                        .traders7d(rollingDto.getUniqueTraders())
                        .buyTraders7d(rollingDto.getBuyUniqueTraders())
                        .sellTraders7d(rollingDto.getSellUniqueTraders())
                        .priceChange7d(BigDecimalFormatUtil.formatBigDecimalForDecimalLength(rollingDto.getTokenPriceChangePercent(),16,6));
                break;
        }
    }

    /**
     * Convert the list of TokenRollingMetricAggregationDto to the list of TokenMetricAggregationDto.
     *
     * @param tokenRollingMetricAggregationDtoList
     *         The list of TokenRollingMetricAggregationDto to be converted.
     * @return The list of TokenMetricAggregationDto.
     */
    private List<TokenMetricAggregationDto> convertTokenMetricAggregationDTO(
            List<TokenRollingMetricAggregationDto> tokenRollingMetricAggregationDtoList) {
        if (CollectionUtils.isEmpty(tokenRollingMetricAggregationDtoList)) {
            return null;
        }
        List<TokenMetricAggregationDto> tokenMetricAggregationDtos = new ArrayList<>();

        // Define the mapping of time types and corresponding getter methods
        Map<String, Function<TokenRollingMetricAggregationDto, TokenRollingMetricDto>> dtoGetters = new HashMap<>();
        dtoGetters.put("1m", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto1m() : null;
        });
        dtoGetters.put("5m", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto5m() : null;
        });
        dtoGetters.put("1h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto1h() : null;
        });
        dtoGetters.put("4h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto4h() : null;
        });
        dtoGetters.put("24h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto24h() : null;
        });
        dtoGetters.put("7d", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto7d() : null;
        });

        for (TokenRollingMetricAggregationDto dto : tokenRollingMetricAggregationDtoList) {
            TokenMetricAggregationDto tokenMetricAggregationDto = new TokenMetricAggregationDto();
            List<TokenMetricDTO> tokenMetricDTOList = new ArrayList<>();

            // Traverse the mapping table to build TokenMetricDTO
            for (Map.Entry<String, Function<TokenRollingMetricAggregationDto, TokenRollingMetricDto>> entry : dtoGetters.entrySet()) {
                String type = entry.getKey();
                TokenRollingMetricDto rollingDto = entry.getValue().apply(dto);
                if (rollingDto != null) {
                    TokenMetricDTO tokenMetricDTO = buildTokenMetricDTO(rollingDto, type);
                    tokenMetricDTOList.add(tokenMetricDTO);
                }
            }

            tokenMetricAggregationDto.setTokenMetricDTOList(tokenMetricDTOList);
            tokenMetricAggregationDto.setPlatform(dto.getPlatform());
            tokenMetricAggregationDto.setAddress(dto.getAddress());
            tokenMetricAggregationDto.setLiquidityUsd(dto.getStats().getTokenRollingMetricDto1m().getLiquidityUsd());
            tokenMetricAggregationDto.setPrice(
                getPlainString(dto.getStats().getTokenRollingMetricDto5m().getTokenPriceUsd()));
            tokenMetricAggregationDto.setMarketCap(
                getPlainString(dto.getStats().getTokenRollingMetricDto5m().getTokenMarketCap()));
            tokenMetricAggregationDto.setBondingCurveRatio(
                    dto.getStats().getTokenRollingMetricDto24h().getBondingCurveRatio());
            tokenMetricAggregationDtos.add(tokenMetricAggregationDto);
        }

        return tokenMetricAggregationDtos;
    }

    /**
     * Helper method to build a TokenMetricDTO object.
     *
     * @param rollingDto
     *         TokenRollingMetricDto object.
     * @param type
     *         Time type, such as "5m", "1h", etc.
     * @return A built TokenMetricDTO object.
     */
    private TokenMetricDTO buildTokenMetricDTO(TokenRollingMetricDto rollingDto, String type) {
        return TokenMetricDTO.builder()
                .priceChange(rollingDto.getTokenPriceChangePercent())
                .volume(rollingDto.getTokenVolume())
                .volumeUsd(rollingDto.getTokenVolumeUsd())
                .buyVolumeUsd(rollingDto.getBuyVolume())
                .sellVolumeUsd(rollingDto.getSellVolume())
                .buyVolumeUsd(rollingDto.getBuyVolumeUsd())
                .sellVolumeUsd(rollingDto.getSellVolumeUsd())
                .txs(rollingDto.getNumTx())
                .buyTxs(rollingDto.getNumBuy())
                .sellTxs(rollingDto.getNumSell())
                .traders(rollingDto.getUniqueTraders())
                .buyTraders(rollingDto.getBuyUniqueTraders())
                .sellTraders(rollingDto.getSellUniqueTraders())
                .tokenPriceHigh(rollingDto.getTokenPriceHigh())
                .tokenPriceLow(rollingDto.getTokenPriceLow())
                .type(type).build();
    }

    /**
     * Construct a list of TokenStatsVO based on the incoming list of TokenRollingMetricAggregationDto.
     *
     * @param tokenRollingMetricAggregationDtoList
     *         The list of TokenRollingMetricAggregationDto to be processed.
     * @return A constructed list of TokenStatsVO.
     */
    public List<TokenStatsVO> convertToTokenStatsVOList(
            List<TokenRollingMetricAggregationDto> tokenRollingMetricAggregationDtoList) {
        if (CollectionUtils.isEmpty(tokenRollingMetricAggregationDtoList)) {
            return Collections.emptyList();
        }

        List<TokenStatsVO> tokenStatsVOList = new ArrayList<>();
        Map<String, Function<TokenRollingMetricAggregationDto, TokenRollingMetricDto>> dtoGetters = new HashMap<>();

        dtoGetters.put("1m", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto1m() : null;
        });
        dtoGetters.put("5m", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto5m() : null;
        });
        dtoGetters.put("1h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto1h() : null;
        });
        dtoGetters.put("4h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto4h() : null;
        });
        dtoGetters.put("24h", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto24h() : null;
        });
        dtoGetters.put("7d", dto -> {
            Stats stats = dto.getStats();
            return stats != null ? stats.getTokenRollingMetricDto7d() : null;
        });

        for (TokenRollingMetricAggregationDto dto : tokenRollingMetricAggregationDtoList) {
            TokenStatsVO tokenStatsVO = new TokenStatsVO();
            tokenStatsVO.setPlatform(dto.getPlatform());
            tokenStatsVO.setAddress(dto.getAddress());
            tokenStatsVO.setLiquidityUsd(dto.getStats().getTokenRollingMetricDto1m().getLiquidityUsd());
            tokenStatsVO.setMarketcap(dto.getMarketCap());
            tokenStatsVO.setBondingCurveRatio(dto.getStats().getTokenRollingMetricDto24h().getBondingCurveRatio());
            if (dto.getTokenPriceUsd() != null) {
                tokenStatsVO.setPriceUsd(dto.getTokenPriceUsd().toPlainString());
            }
            if (dto.getTokenPriceNative() != null) {
                tokenStatsVO.setPrice(dto.getTokenPriceNative().toPlainString());
            }
            if (dto.getStats()!=null && dto.getStats().getTokenRollingMetricDto24h()!=null&&
                    dto.getStats().getTokenRollingMetricDto24h().getTokenPriceHigh() != null) {
                tokenStatsVO.setPriceHigh(dto.getStats().getTokenRollingMetricDto24h().getTokenPriceHigh().toPlainString());
            }
            if (dto.getStats()!=null && dto.getStats().getTokenRollingMetricDto24h()!=null&&
                    dto.getStats().getTokenRollingMetricDto24h().getTokenPriceLow() != null) {
                tokenStatsVO.setPriceLow(dto.getStats().getTokenRollingMetricDto24h().getTokenPriceLow().toPlainString());
            }

            dtoGetters.forEach((time, getter) -> {
                TokenRollingMetricDto rollingDto = getter.apply(dto);
                if (rollingDto != null) {
                    updateTokenStatsVOWithRollingDto(tokenStatsVO, rollingDto, time);
                }
            });

            tokenStatsVOList.add(tokenStatsVO);
        }

        return tokenStatsVOList;
    }

    /**
     * Update the TokenStatsVO with the information from TokenRollingMetricDto.
     *
     * @param tokenStatsVO
     *         The TokenStatsVO object to be updated.
     * @param rollingDto
     *         The TokenRollingMetricDto object containing the information.
     * @param time
     *         The time period, such as "5m", "1h", etc.
     */
    private void updateTokenStatsVOWithRollingDto(TokenStatsVO tokenStatsVO, TokenRollingMetricDto rollingDto,
            String time) {
        switch (time) {
            case "1m":
                tokenStatsVO.setVolume1m(rollingDto.getTokenVolume());
                tokenStatsVO.setVolumeUsd1m(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setBuyVolume1m(rollingDto.getBuyVolume());
                tokenStatsVO.setSellVolume1m(rollingDto.getSellVolume());
                tokenStatsVO.setBuyVolumeUsd1m(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setSellVolumeUsd1m(rollingDto.getSellVolumeUsd());
                tokenStatsVO.setTxs1m(rollingDto.getNumTx());
                tokenStatsVO.setBuyTxs1m(rollingDto.getNumBuy());
                tokenStatsVO.setSellTxs1m(rollingDto.getNumSell());
                tokenStatsVO.setPriceChange1m(rollingDto.getTokenPriceChangePercent());
                tokenStatsVO.setTraders1m(rollingDto.getUniqueTraders());
                tokenStatsVO.setBuyTraders1m(rollingDto.getBuyUniqueTraders());
                tokenStatsVO.setSellTraders1m(rollingDto.getSellUniqueTraders());
                break;
            case "5m":
                tokenStatsVO.setVolume5m(rollingDto.getTokenVolume());
                tokenStatsVO.setVolumeUsd5m(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setBuyVolume5m(rollingDto.getBuyVolume());
                tokenStatsVO.setSellVolume5m(rollingDto.getSellVolume());
                tokenStatsVO.setBuyVolumeUsd5m(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setSellVolumeUsd5m(rollingDto.getSellVolumeUsd());
                tokenStatsVO.setTxs5m(rollingDto.getNumTx());
                tokenStatsVO.setBuyTxs5m(rollingDto.getNumBuy());
                tokenStatsVO.setSellTxs5m(rollingDto.getNumSell());
                tokenStatsVO.setPriceChange5m(rollingDto.getTokenPriceChangePercent());
                tokenStatsVO.setTraders5m(rollingDto.getUniqueTraders());
                tokenStatsVO.setBuyTraders5m(rollingDto.getBuyUniqueTraders());
                tokenStatsVO.setSellTraders5m(rollingDto.getSellUniqueTraders());
                break;
            case "1h":
                tokenStatsVO.setVolume1h(rollingDto.getTokenVolume());
                tokenStatsVO.setVolumeUsd1h(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setBuyVolume1h(rollingDto.getBuyVolume());
                tokenStatsVO.setSellVolume1h(rollingDto.getSellVolume());
                tokenStatsVO.setBuyVolumeUsd1h(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setSellVolumeUsd1h(rollingDto.getSellVolumeUsd());
                tokenStatsVO.setTxs1h(rollingDto.getNumTx());
                tokenStatsVO.setBuyTxs1h(rollingDto.getNumBuy());
                tokenStatsVO.setSellTxs1h(rollingDto.getNumSell());
                tokenStatsVO.setPriceChange1h(rollingDto.getTokenPriceChangePercent());
                tokenStatsVO.setTraders1h(rollingDto.getUniqueTraders());
                tokenStatsVO.setBuyTraders1h(rollingDto.getBuyUniqueTraders());
                tokenStatsVO.setSellTraders1h(rollingDto.getSellUniqueTraders());
                break;
            case "4h":
                tokenStatsVO.setVolume4h(rollingDto.getTokenVolume());
                tokenStatsVO.setVolumeUsd4h(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setBuyVolume4h(rollingDto.getBuyVolume());
                tokenStatsVO.setSellVolume4h(rollingDto.getSellVolume());
                tokenStatsVO.setBuyVolumeUsd4h(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setSellVolumeUsd4h(rollingDto.getSellVolumeUsd());
                tokenStatsVO.setTxs4h(rollingDto.getNumTx());
                tokenStatsVO.setBuyTxs4h(rollingDto.getNumBuy());
                tokenStatsVO.setSellTxs4h(rollingDto.getNumSell());
                tokenStatsVO.setPriceChange4h(rollingDto.getTokenPriceChangePercent());
                tokenStatsVO.setTraders4h(rollingDto.getUniqueTraders());
                tokenStatsVO.setBuyTraders4h(rollingDto.getBuyUniqueTraders());
                tokenStatsVO.setSellTraders4h(rollingDto.getSellUniqueTraders());
                break;
            case "24h":
                tokenStatsVO.setVolume24h(rollingDto.getTokenVolume());
                tokenStatsVO.setVolumeUsd24h(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setBuyVolume24h(rollingDto.getBuyVolume());
                tokenStatsVO.setSellVolume24h(rollingDto.getSellVolume());
                tokenStatsVO.setBuyVolumeUsd24h(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setSellVolumeUsd24h(rollingDto.getSellVolumeUsd());
                tokenStatsVO.setTxs24h(rollingDto.getNumTx());
                tokenStatsVO.setBuyTxs24h(rollingDto.getNumBuy());
                tokenStatsVO.setSellTxs24h(rollingDto.getNumSell());
                tokenStatsVO.setPriceChange24h(rollingDto.getTokenPriceChangePercent());
                tokenStatsVO.setTraders24h(rollingDto.getUniqueTraders());
                tokenStatsVO.setBuyTraders24h(rollingDto.getBuyUniqueTraders());
                tokenStatsVO.setSellTraders24h(rollingDto.getSellUniqueTraders());
                break;
            case "7d":
                tokenStatsVO.setVolume7d(rollingDto.getTokenVolume());
                tokenStatsVO.setVolumeUsd7d(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setBuyVolume7d(rollingDto.getBuyVolume());
                tokenStatsVO.setSellVolume7d(rollingDto.getSellVolume());
                tokenStatsVO.setBuyVolumeUsd7d(rollingDto.getTokenVolumeUsd());
                tokenStatsVO.setSellVolumeUsd7d(rollingDto.getSellVolumeUsd());
                tokenStatsVO.setTxs7d(rollingDto.getNumTx());
                tokenStatsVO.setBuyTxs7d(rollingDto.getNumBuy());
                tokenStatsVO.setSellTxs7d(rollingDto.getNumSell());
                tokenStatsVO.setPriceChange7d(rollingDto.getTokenPriceChangePercent());
                tokenStatsVO.setTraders7d(rollingDto.getUniqueTraders());
                tokenStatsVO.setBuyTraders7d(rollingDto.getBuyUniqueTraders());
                tokenStatsVO.setSellTraders7d(rollingDto.getSellUniqueTraders());
                break;
        }
    }

    /**
     * Build a WebsocketCustomEventDTO object based on TokenMetricAggregationDto.
     *
     * @param tokenMetricAggregationDto
     *         The TokenMetricAggregationDto object.
     * @return A built WebsocketCustomEventDTO object.
     */
    private WebsocketCustomEventDTO<TokenMetricAggregationDto> buildWebsocketCustomEventDTO(
            TokenMetricAggregationDto tokenMetricAggregationDto) {
        return WebsocketCustomEventDTO.<TokenMetricAggregationDto>builder()
                .businessTopic(WebsocketBusinessTopicEnum.TOKEN_METRIC_DATA)
                .tags(StringUtils.join(tokenMetricAggregationDto.getPlatform().toString(), StringPool.UNDERLINE,
                        tokenMetricAggregationDto.getAddress())).eventId(
                        StringUtils.join(tokenMetricAggregationDto.getPlatform().toString(), StringPool.UNDERLINE,
                                tokenMetricAggregationDto.getAddress())).metaData(() -> tokenMetricAggregationDto)
                .build();
    }

    private String getPlainString(BigDecimal bigDecimal) {
        if (bigDecimal == null) {
            return null;
        }
        return bigDecimal.toPlainString();
    }

}