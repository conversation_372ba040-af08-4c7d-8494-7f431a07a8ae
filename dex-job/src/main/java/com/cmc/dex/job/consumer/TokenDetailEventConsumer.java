package com.cmc.dex.job.consumer;

import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.job.consumer.handler.PoolTokenDetailConsumerHandler;
import com.cmc.framework.utils.JacksonUtils;
import cn.hutool.core.thread.ThreadFactoryBuilder;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.DisposableBean;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import com.google.common.collect.Lists;

import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.SynchronousQueue;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicBoolean;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class TokenDetailEventConsumer extends AbstractManualCommitConsumer<DexTokenDetailEntity>
    implements DisposableBean {

    @Autowired
    private PoolTokenDetailConsumerHandler poolTokenDetailConsumerHandler;
    @Value("${cmc.dex-job.token.detail.consumer.active.switch:true}")
    private Boolean switchButton;
    @Value("${token.metrics.detail.handler.corePoolSize:5}")
    private int corePoolSize;
    @Value("${token.metrics.detail.handler.maximumPoolSize:50}")
    private int maximumPoolSize;
    @Value("${token.metrics.detail.handler.keepAliveTime:60}")
    private int keepAliveTime;
    @Value("${token.metrics.detail.handler.batchSize:50}")
    private int batchSize;

    private ExecutorService executorService;

    private final AtomicBoolean running = new AtomicBoolean(true);

    @PostConstruct
    public void init() {
        executorService = new ThreadPoolExecutor(corePoolSize, maximumPoolSize, keepAliveTime, TimeUnit.SECONDS,
            new SynchronousQueue<>(), new ThreadFactoryBuilder().setNamePrefix("token_detail_event_consumer").build(),
            new ThreadPoolExecutor.CallerRunsPolicy());
    }

    @Override
    protected boolean process(List<DexTokenDetailEntity> records) {
        try {
            if (!switchButton) {
                log.info("Consumer is off by control,this switch key is [{}] in apollo",
                    "cmc.dex-job.token.detail.consumer.active.switch");
                return true;
            }
            log.debug("TokenDetailEventConsumer-PoolTokensPairsConsumer receive kafka message:{}",
                JacksonUtils.serialize(records));

            if (!running.get()) {
                log.info("TokenDetailEventConsumer stopped");
                return false;
            }

            List<List<DexTokenDetailEntity>> partitionedRecords = Lists.partition(records, batchSize);
            for (List<DexTokenDetailEntity> subList : partitionedRecords) {
                executorService.submit(() -> poolTokenDetailConsumerHandler.handleDexTokenDetailEntityEvent(subList));
            }
        } catch (Exception e) {
            log.error("TokenDetailEventConsumer deal exception,the message: {}", JacksonUtils.serialize(records), e);
            return false; // Return false if an exception occurs
        }
        return true;
    }

    @PreDestroy
    public void destroy() {
        log.info("Shutting down TokenDetailEventConsumer");
        running.set(false);
        try {
            if (!this.executorService.awaitTermination(90, TimeUnit.SECONDS)) {
                log.warn("Shutdown timeout, force shutdown");
                this.executorService.shutdownNow();
            }
        } catch (InterruptedException var2) {
            this.executorService.shutdownNow();
            Thread.currentThread().interrupt();
        }
    }
}