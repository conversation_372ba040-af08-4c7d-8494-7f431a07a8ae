package com.cmc.dex.job.handler.meme;

import static com.cmc.dex.common.constants.MetricConstants.CMC_DQUERY_JOB_HANDLER_ERRORS;
import static com.cmc.dex.common.constants.MetricConstants.TAG_JOB_NAME;

import com.cmc.dex.job.service.launch.DevMigrateHisRefreshService;
import com.cmc.framework.job.BaseJobHandler;
import com.cmc.framework.job.JobHandlingResult;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;


/**
 * TopTradeSyncProdHandler
 * <AUTHOR> ricky.x
 * @date: 2025/5/21 15:54
 */
@Component
@Slf4j
public class DevMigrateHisRefreshHandler extends BaseJobHandler<String, String> {

    @Autowired
    private DevMigrateHisRefreshService devMigrateHisRefreshService;

    @Autowired
    private CmcMeterRegistry cmcMeterRegistry;

    @SneakyThrows
    @Override
    public JobHandlingResult<String> doInHandle(String args) {
        try {
            devMigrateHisRefreshService.process().toFuture().get();
        }catch (Exception e) {
            log.error("DevMigrateHisRefreshHandler failed, error:", e);
            cmcMeterRegistry.counter(CMC_DQUERY_JOB_HANDLER_ERRORS,TAG_JOB_NAME,"DevMigrateHisRefreshHandler");
            return new JobHandlingResult<>(false, e.getMessage());
        }
        return new JobHandlingResult<>(true, StringUtils.EMPTY);
    }

}
