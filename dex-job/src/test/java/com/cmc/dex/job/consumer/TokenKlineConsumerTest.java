package com.cmc.dex.job.consumer;

import static org.mockito.ArgumentMatchers.anyIterable;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.atLeastOnce;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.dex.dao.repository.ddb.DexKlineRepository;
import com.cmc.dex.job.config.KlineConsumerConfig;
import com.cmc.dex.job.config.KlineSwitchConfig;
import com.cmc.dex.job.consumer.handler.KlineProcessor;
import com.cmc.dex.model.enums.KlineTypeEnum;
import com.cmc.dex.model.kafka.KlineMessage;
import com.cmc.framework.metrics.CmcMeterRegistry;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import io.micrometer.core.instrument.Meter;
import io.micrometer.core.instrument.Tags;
import io.micrometer.core.instrument.noop.NoopCounter;
import io.micrometer.core.instrument.noop.NoopTimer;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

public class TokenKlineConsumerTest {

    @InjectMocks
    private TokenKlineConsumer tokenKlineConsumer;

    @Mock
    private DexKlineRepository dexKlineRepository;

    @Mock
    private KlineConsumerConfig config;

    @Mock
    private KlineSwitchConfig klineSwitchConfig;

    @Mock
    private KlineProcessor klineProcessor;

    @Mock
    private CmcMeterRegistry cmcMeterRegistry;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);

        when(config.getTokenWebsocketSwitch()).thenReturn(true);
        when(config.getTokenPushSwitch()).thenReturn(true);
        when(config.getTokenDdbSwitch()).thenReturn(true);
        when(config.getTokenDdbConcurrencyCount()).thenReturn(1);
        when(config.getTokenWsConcurrencyCount()).thenReturn(1);
        when(config.getTokenWsTypes()).thenReturn("u,n,q");
        when(config.getCaseSensitiveSet()).thenReturn(Set.of(16));
        when(klineSwitchConfig.getPkSet()).thenReturn(Set.of("-1"));

        ReflectionTestUtils.setField(DexSchedulers.class, "BUSINESS", Schedulers.boundedElastic());

    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    /**
     * Test that the pull method processes multiple valid records concurrently.
     */

    @Test
    public void testSuccessfulProcessing() {
        List<KlineMessage> records = new ArrayList<>();
        KlineMessage message1 = new KlineMessage();
        message1.setPk("1-aBc");
        message1.setTs(1746057599000L);
        message1.setInterval("1min");

        KlineMessage message2 = new KlineMessage();
        message2.setPk("16-def");
        message2.setTs(1746057599000L);
        message2.setInterval("3min");

        records.add(message1);
        records.add(message2);

        when(dexKlineRepository.batchSaveRecord(any(), any())).thenReturn(Mono.empty());
        when(klineProcessor.resetMessage(any())).thenReturn(records);
        when(klineProcessor.sendWs(any(), any(), any())).thenReturn(Mono.empty());
        when(klineProcessor.sendPushMessage(any(), any())).thenReturn(Mono.empty());
        when(cmcMeterRegistry.timer(any(), anyIterable())).thenReturn(
            new NoopTimer(new Meter.Id("1", Tags.of("1", "1"), null, null, Meter.Type.TIMER)));
        when(cmcMeterRegistry.counter(any(), anyIterable())).thenReturn(
            new NoopCounter(new Meter.Id("1", Tags.of("1", "1"), null, null, Meter.Type.COUNTER)));

        tokenKlineConsumer.process(records);

        // Assert
        verify(dexKlineRepository, atLeastOnce()).batchSaveRecord(any(), any(KlineTypeEnum.class));
        verify(klineProcessor, atLeastOnce()).sendWs(any(), any(), any());
        verify(klineProcessor, atLeastOnce()).sendPushMessage(any(), any());

    }
}
