package com.cmc.dex.job.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.dex.business.client.CmcDexServiceClient;
import com.cmc.dex.business.client.model.PairInfoDTO;
import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.dex.dao.entity.ddb.CmcKlineEntity;
import com.cmc.dex.dao.entity.mongodb.pool.PoolMapEntity;
import com.cmc.dex.dao.repository.ddb.CmcKlineRepository;
import com.cmc.dex.dao.repository.ddb.DexKlineRepository;
import com.cmc.dex.dao.repository.mongodb.dexer.PoolMapRepository;
import com.cmc.framework.job.JobHandlingResult;
import java.util.ArrayList;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;
import software.amazon.awssdk.enhanced.dynamodb.model.Page;

public class KlineDataMigrationHandlerTest {

    @InjectMocks
    private KlineDataMigrationHandler klineDataMigrationHandler;

    @Mock
    private DexKlineRepository dexKlineRepository;
    @Mock
    private PoolMapRepository poolMapRepository;
    @Mock
    private CmcKlineRepository cmcKlineRepository;
    @Mock
    private CmcDexServiceClient cmcDexServiceClient;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);

        ReflectionTestUtils.setField(klineDataMigrationHandler, "poolBatchSize", 100);
        ReflectionTestUtils.setField(klineDataMigrationHandler, "defaultBacktraceDay", 100);
        ReflectionTestUtils.setField(klineDataMigrationHandler, "queryLimit", 100);
        ReflectionTestUtils.setField(klineDataMigrationHandler, "tableNum", 8);
        ReflectionTestUtils.setField(klineDataMigrationHandler, "jobSwitch", true);
        ReflectionTestUtils.setField(klineDataMigrationHandler, "poolThreads", 8);
        ReflectionTestUtils.setField(klineDataMigrationHandler, "ddbThreads", 8);
        ReflectionTestUtils.setField(klineDataMigrationHandler, "queryConcurrency", 8);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testDoInHandle() {
        MockedStatic<DexSchedulers> dexSchedulersMockedStatic = mockStatic(DexSchedulers.class);
        when(DexSchedulers.business()).thenReturn(Schedulers.boundedElastic());
        String args =
            "{\"klineType\":\"1m\",\"shardingTotal\":1,\"shardingIndex\":0,\"startTime\":\"2025-01-01 00:00:00\",\"endTime\":\"2025-01-02 00:00:00\"}";
        //KlineDataMigrationJobParam jobParam = JobParamParser.parse(args, KlineDataMigrationJobParam.class);

        List<PoolMapEntity> list = new ArrayList<>();
        list.add(PoolMapEntity.builder().id(1L).platformId(1).address("abc").build());

        List<CmcKlineEntity> entities = new ArrayList<>();
        entities.add(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build());
        Page<CmcKlineEntity> page = Page.create(entities);

        when(dexKlineRepository.get(any(), any())).thenReturn(Mono.empty());
        when(dexKlineRepository.save(any())).thenReturn(Mono.empty());
        when(poolMapRepository.queryScroll(eq(0L), eq(100))).thenReturn(Mono.just(list));
        when(poolMapRepository.queryScroll(eq(1L), eq(100))).thenReturn(Mono.just(new ArrayList<>()));
        when(dexKlineRepository.batchSaveRecord(any(), any())).thenReturn(Mono.empty());
        when(cmcKlineRepository.betweenTimePeriodScan(any(), any(), any(), any(), any(), any())).thenReturn(
            Mono.just(page));

        JobHandlingResult<String> result = klineDataMigrationHandler.doInHandle(args);

        Assert.assertTrue(result.isSucceeded());

        dexSchedulersMockedStatic.close();
    }

}
