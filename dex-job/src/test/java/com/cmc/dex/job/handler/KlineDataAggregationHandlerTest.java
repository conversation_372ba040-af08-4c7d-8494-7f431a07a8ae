package com.cmc.dex.job.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;

import com.cmc.dex.dao.entity.ddb.CmcKlineEntity;
import com.cmc.dex.dao.entity.ddb.KlineRecordEntity;
import com.cmc.dex.dao.entity.mongodb.pool.PoolMapEntity;
import com.cmc.dex.dao.repository.ddb.CmcKlineRepository;
import com.cmc.dex.dao.repository.ddb.DexKlineRepository;
import com.cmc.dex.dao.repository.mongodb.dexer.PoolMapRepository;
import com.cmc.framework.job.JobHandlingResult;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.Assert;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import software.amazon.awssdk.enhanced.dynamodb.model.BatchWriteResult;

public class KlineDataAggregationHandlerTest {
    @InjectMocks
    private KlineDataAggregationHandler klineDataAggregationHandler;

    @Mock
    private DexKlineRepository dexKlineRepository;
    @Mock
    private PoolMapRepository poolMapRepository;
    @Mock
    private CmcKlineRepository cmcKlineRepository;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);

        ReflectionTestUtils.setField(klineDataAggregationHandler, "poolBatchSize", 100);
        ReflectionTestUtils.setField(klineDataAggregationHandler, "defaultBacktraceDay", 100);
        ReflectionTestUtils.setField(klineDataAggregationHandler, "tableNum", 8);
        ReflectionTestUtils.setField(klineDataAggregationHandler, "jobSwitch", true);
        ReflectionTestUtils.setField(klineDataAggregationHandler, "poolThreads", 8);
        ReflectionTestUtils.setField(klineDataAggregationHandler, "aggThreads", 8);
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testHourAgg() {
        String args =
            "{\"klineAggType\":\"HourKlineAgg\",\"allowKlineTypes\": \"2h,6h,8h,12h\",\"shardingTotal\":1,\"shardingIndex\":0,\"startTime\":\"2025-01-01 00:00:00\",\"endTime\":\"2025-01-02 00:00:00\"}";

        List<PoolMapEntity> list = new ArrayList<>();
        list.add(PoolMapEntity.builder().id(10L).platformId(1).address("abc").build());

        List<CmcKlineEntity> entities = new ArrayList<>();
        entities.add(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build());

        when(dexKlineRepository.get(any(), any())).thenReturn(Mono.empty());
        when(dexKlineRepository.save(any())).thenReturn(Mono.empty());
        when(poolMapRepository.queryScroll(eq(0L), any())).thenReturn(Mono.just(list));
        when(poolMapRepository.queryScroll(eq(10L), any())).thenReturn(Mono.just(new ArrayList<>()));
        when(dexKlineRepository.batchSaveRecord(any(), any())).thenReturn(
            Mono.just(BatchWriteResult.builder().unprocessedRequests(new HashMap<>()).build()));
        when(cmcKlineRepository.betweenTimePeriodAsc(any(), any(), any(), any(), any())).thenReturn(
            Mono.just(entities));

        when(cmcKlineRepository.queryNextRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));
        when(cmcKlineRepository.queryLatestRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));

        JobHandlingResult<String> result = klineDataAggregationHandler.doInHandle(args);

        Assert.assertTrue(result.isSucceeded());
    }

    @Test
    public void testDayAgg() {
        String args =
            "{\"klineAggType\":\"DayKlineAgg\",\"allowKlineTypes\": \"3d\",\"shardingTotal\":1,\"shardingIndex\":0,\"startTime\":\"2025-01-01 00:00:00\",\"endTime\":\"2025-01-02 00:00:00\"}";

        List<PoolMapEntity> list = new ArrayList<>();
        list.add(PoolMapEntity.builder().id(10L).platformId(1).address("abc").build());

        List<CmcKlineEntity> entities = new ArrayList<>();
        entities.add(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build());

        List<KlineRecordEntity> klineRecordEntities = new ArrayList<>();
        klineRecordEntities.add(KlineRecordEntity.builder().pk("10-abc").ts(1747267200000L).build());

        when(dexKlineRepository.get(any(), any())).thenReturn(Mono.empty());
        when(dexKlineRepository.save(any())).thenReturn(Mono.empty());
        when(poolMapRepository.queryScroll(eq(0L), any())).thenReturn(Mono.just(list));
        when(poolMapRepository.queryScroll(eq(10L), any())).thenReturn(Mono.just(new ArrayList<>()));
        when(dexKlineRepository.batchSaveRecord(any(), any())).thenReturn(
            Mono.just(BatchWriteResult.builder().unprocessedRequests(new HashMap<>()).build()));
        when(cmcKlineRepository.betweenTimePeriodAsc(any(), any(), any(), any(), any())).thenReturn(
            Mono.just(entities));
        when(dexKlineRepository.betweenTimePeriodAsc(any(), any(), any(), any(), any())).thenReturn(
            Mono.just(klineRecordEntities));
        when(dexKlineRepository.deleteByTimePeriod(any(), any(), any())).thenReturn(Mono.empty());

        when(cmcKlineRepository.queryFirstRecord(any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));
        when(cmcKlineRepository.queryNextRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));
        when(cmcKlineRepository.queryLatestRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));

        JobHandlingResult<String> result = klineDataAggregationHandler.doInHandle(args);

        Assert.assertTrue(result.isSucceeded());
    }

    @Test
    public void testWeekAgg() {
        String args =
            "{\"klineAggType\":\"WeekKlineAgg\",\"allowKlineTypes\": \"1w\",\"shardingTotal\":1,\"shardingIndex\":0,\"startTime\":\"2025-01-01 00:00:00\",\"endTime\":\"2025-01-02 00:00:00\"}";

        List<PoolMapEntity> list = new ArrayList<>();
        list.add(PoolMapEntity.builder().id(10L).platformId(1).address("abc").build());

        List<CmcKlineEntity> entities = new ArrayList<>();
        entities.add(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build());

        when(dexKlineRepository.get(any(), any())).thenReturn(Mono.empty());
        when(dexKlineRepository.save(any())).thenReturn(Mono.empty());
        when(poolMapRepository.queryScroll(eq(0L), any())).thenReturn(Mono.just(list));
        when(poolMapRepository.queryScroll(eq(10L), any())).thenReturn(Mono.just(new ArrayList<>()));
        when(dexKlineRepository.batchSaveRecord(any(), any())).thenReturn(
            Mono.just(BatchWriteResult.builder().unprocessedRequests(new HashMap<>()).build()));
        when(cmcKlineRepository.betweenTimePeriodAsc(any(), any(), any(), any(), any())).thenReturn(
            Mono.just(entities));

        when(cmcKlineRepository.queryNextRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));
        when(cmcKlineRepository.queryLatestRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));

        JobHandlingResult<String> result = klineDataAggregationHandler.doInHandle(args);

        Assert.assertTrue(result.isSucceeded());
    }

    @Test
    public void tesMonthAgg() {
        String args =
            "{\"klineAggType\":\"MonthKlineAgg\",\"allowKlineTypes\": \"1M\",\"shardingTotal\":1,\"shardingIndex\":0,\"startTime\":\"2025-01-01 00:00:00\",\"endTime\":\"2025-01-02 00:00:00\"}";

        List<PoolMapEntity> list = new ArrayList<>();
        list.add(PoolMapEntity.builder().id(10L).platformId(1).address("abc").build());

        List<CmcKlineEntity> entities = new ArrayList<>();
        entities.add(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build());

        when(dexKlineRepository.get(any(), any())).thenReturn(Mono.empty());
        when(dexKlineRepository.save(any())).thenReturn(Mono.empty());
        when(poolMapRepository.queryScroll(eq(0L), any())).thenReturn(Mono.just(list));
        when(poolMapRepository.queryScroll(eq(10L), any())).thenReturn(Mono.just(new ArrayList<>()));
        when(dexKlineRepository.batchSaveRecord(any(), any())).thenReturn(
            Mono.just(BatchWriteResult.builder().unprocessedRequests(new HashMap<>()).build()));
        when(cmcKlineRepository.betweenTimePeriodAsc(any(), any(), any(), any(), any())).thenReturn(
            Mono.just(entities));

        when(cmcKlineRepository.queryNextRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));
        when(cmcKlineRepository.queryLatestRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));

        JobHandlingResult<String> result = klineDataAggregationHandler.doInHandle(args);

        Assert.assertTrue(result.isSucceeded());
    }

    @Test
    public void tesMinuteAgg() {
        String args =
            "{\"klineAggType\":\"MinuteKlineAgg\",\"allowKlineTypes\": \"3m,30m\",\"shardingTotal\":1,\"shardingIndex\":0,\"startTime\":\"2025-01-01 00:00:00\",\"endTime\":\"2025-01-02 00:00:00\"}";

        List<PoolMapEntity> list = new ArrayList<>();
        list.add(PoolMapEntity.builder().id(10L).platformId(1).address("abc").build());

        List<CmcKlineEntity> entities = new ArrayList<>();
        entities.add(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build());

        when(dexKlineRepository.get(any(), any())).thenReturn(Mono.empty());
        when(dexKlineRepository.save(any())).thenReturn(Mono.empty());
        when(poolMapRepository.queryScroll(eq(0L), any())).thenReturn(Mono.just(list));
        when(poolMapRepository.queryScroll(eq(10L), any())).thenReturn(Mono.just(new ArrayList<>()));
        when(dexKlineRepository.batchSaveRecord(any(), any())).thenReturn(
            Mono.just(BatchWriteResult.builder().unprocessedRequests(new HashMap<>()).build()));
        when(cmcKlineRepository.betweenTimePeriodAsc(any(), any(), any(), any(), any())).thenReturn(
            Mono.just(entities));

        when(cmcKlineRepository.queryNextRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));
        when(cmcKlineRepository.queryLatestRecord(any(), any(), any())).thenReturn(
            Mono.just(CmcKlineEntity.builder().poolId("10").timePeriod(1747267200000L).build()));

        JobHandlingResult<String> result = klineDataAggregationHandler.doInHandle(args);

        Assert.assertTrue(result.isSucceeded());
    }
}
