<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.cmc</groupId>
    <artifactId>dquery-dex-service</artifactId>
    <version>1.0.40-SNAPSHOT</version>
  </parent>

  <groupId>com.cmc</groupId>
  <artifactId>dex-job</artifactId>
  <version>1.0.40-SNAPSHOT</version>
<dependencies>
    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>dex-dao</artifactId>
    </dependency>
    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>dex-common</artifactId>
    </dependency>
    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>dex-model</artifactId>
    </dependency>
    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>dex-business</artifactId>
    </dependency>

    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-utils</artifactId>
    </dependency>
    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-apollo</artifactId>
    </dependency>
    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-boot-starter-webflux</artifactId>
    </dependency>
    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-job</artifactId>
    </dependency>

    <dependency>
        <groupId>com.github.luben</groupId>
        <artifactId>zstd-jni</artifactId>
        <version>1.5.7-1</version>
    </dependency>

    <dependency>
        <artifactId>rsocket-transport-netty</artifactId>
        <groupId>io.rsocket</groupId>
        <scope>compile</scope>
    </dependency>
    <dependency>
        <artifactId>rsocket-core</artifactId>
        <groupId>io.rsocket</groupId>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-common</artifactId>
    </dependency>
    <dependency>
      <groupId>jakarta.annotation</groupId>
      <artifactId>jakarta.annotation-api</artifactId>
    </dependency>

    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-apollo</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-webflux</artifactId>
    </dependency>
    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-test</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mockito</groupId>
      <artifactId>mockito-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework</groupId>
      <artifactId>spring-test</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-boot-starter-metrics</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-metrics</artifactId>
        <exclusions>
            <exclusion>
                <artifactId>kafka-clients</artifactId>
                <groupId>org.apache.kafka</groupId>
            </exclusion>
        </exclusions>
    </dependency>
    <dependency>
        <groupId>com.baomidou</groupId>
        <artifactId>mybatis-plus-boot-starter</artifactId>
    </dependency>
        <!-- Java Bean Validation API -->
        <dependency>
            <groupId>jakarta.validation</groupId>
            <artifactId>jakarta.validation-api</artifactId>
        </dependency>
        <!-- Hibernate Validator -->
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
    <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-boot-starter-mongodb-reactive</artifactId>
        <version>2.1.0</version>
        <scope>compile</scope>
    </dependency>

    <dependency>
        <groupId>org.springframework.data</groupId>
        <artifactId>spring-data-mongodb</artifactId>
    </dependency>
    <dependency>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-data-mongodb-reactive</artifactId>
    </dependency>
</dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <finalName>dquery-dex-job</finalName>
        </configuration>
      </plugin>
      <plugin>
        <groupId>com.spotify</groupId>
        <artifactId>dockerfile-maven-plugin</artifactId>
        <!--
          TODO: the entire path should match corresponding path in the skaffold.yaml file.
          For example, if your image tag in skaffold.yaml is 346764516239.dkr.ecr.us-east-1.amazonaws.com/cmc-sample-job
          Then you will need to have docker.root.repo = 346764516239.dkr.ecr.us-east-1.amazonaws.com
          and ${docker.root.repo}/cmc-sample-job in this <repository> tag.
           -->
        <configuration>
          <repository>${docker.root.repo}/dquery-dex-job</repository>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>