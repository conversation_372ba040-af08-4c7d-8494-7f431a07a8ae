package com.cmc.dex.model.token;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * MigrateCountDTO
 * <AUTHOR> ricky.x
 * @date: 2025/6/5 09:07
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MigrateCountDTO {

    @JsonProperty("pid")
    private Integer platformId;

    @JsonProperty("crt")
    private String creator;

    @JsonProperty("cnt")
    private Integer count;

    @JsonProperty("lts")
    private List<String> latestTokens;

}