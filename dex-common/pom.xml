<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.cmc</groupId>
    <artifactId>dquery-dex-service</artifactId>
    <version>1.0.40-SNAPSHOT</version>
  </parent>

  <groupId>com.cmc</groupId>
  <artifactId>dex-common</artifactId>
  <version>1.0.40-SNAPSHOT</version>
    <dependencies>
      <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-utils</artifactId>
      </dependency>
      <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-metrics</artifactId>
      </dependency>
      <dependency>
        <groupId>com.fasterxml.jackson.core</groupId>
        <artifactId>jackson-databind</artifactId>
      </dependency>
      <dependency>
        <groupId>jakarta.annotation</groupId>
        <artifactId>jakarta.annotation-api</artifactId>
      </dependency>
      <dependency>
        <groupId>org.projectlombok</groupId>
        <artifactId>lombok</artifactId>
        <scope>provided</scope>
      </dependency>
      <dependency>
          <groupId>org.springframework</groupId>
          <artifactId>spring-beans</artifactId>
      </dependency>
      <dependency>
        <groupId>org.mapstruct</groupId>
        <artifactId>mapstruct</artifactId>
      </dependency>
      <dependency>
        <groupId>com.cmc</groupId>
        <artifactId>cmc-framework-common</artifactId>
      </dependency>
      <dependency>
        <groupId>com.squareup.okhttp3</groupId>
        <artifactId>okhttp</artifactId>
        <version>4.9.0</version> <!-- 请检查最新版本 -->
      </dependency>
      <dependency>
        <groupId>com.ctrip.framework.apollo</groupId>
        <artifactId>apollo-core</artifactId>
        <version>1.9.0</version>
        <scope>compile</scope>
      </dependency>
        <dependency>
            <groupId>com.cmc</groupId>
            <artifactId>dex-model</artifactId>
        </dependency>
      <dependency>
        <groupId>io.projectreactor</groupId>
        <artifactId>reactor-core-micrometer</artifactId>
      </dependency>
    </dependencies>

    <build>
    <plugins>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>