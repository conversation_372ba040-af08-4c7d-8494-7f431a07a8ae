package com.cmc.dex.common.constants;

/**
 * RedisConstants
 * <AUTHOR> ricky.x
 * @date: 2025/4/29 14:11
 */
public class RedisConstants {

    public static final String API_TOP_TRADER = "v1:dquery:top-trader:%s";

    public static final String API_PLATFORM = "v1:platform";

    public static final String TOKEN_SECURITY_SYNC_KEY = "v1:token-security:sync:%s:%s";

    public static final String TOKEN_SECURITY_SYNC_INTERVAL_KEY = "v1:token-security:sync:interval:%s:%s";

    public static final String TOKEN_SECURITY_MARK_CACHE_KEY = "v1:token-security:mark:key";

    public static final String DEV_MIGRATE_COUNT = "v1:dev-migrate-count:%s";

    public static final String SEARCH_HOT_WORDS_KEY = "search:hotwords:{token}";

    public static final String SEARCH_HOT_WORDS_TIME_HASH = "search:hotwords:time:{token}";

    public static final String HOT_WORD_UPDATE_LUA_SCRIPT = """
            local lastTime = redis.call('hget', KEYS[2], ARGV[1])
            if not lastTime then lastTime = ARGV[2] end
            local oldScore = redis.call('zscore', KEYS[1], ARGV[1])
            if not oldScore then oldScore = 0 end
            local hourDiff = math.floor((ARGV[2] - lastTime) / 3600000)
            local decay = math.pow(0.9, hourDiff)
            local newScore = oldScore * decay + 1
            redis.call('zadd', KEYS[1], newScore, ARGV[1])
            redis.call('hset', KEYS[2], ARGV[1], ARGV[2])
            return 1
        """;

}
