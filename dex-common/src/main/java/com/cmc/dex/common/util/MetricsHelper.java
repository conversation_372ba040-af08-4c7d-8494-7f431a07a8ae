package com.cmc.dex.common.util;

import io.micrometer.core.instrument.MeterRegistry;
import io.micrometer.core.instrument.Metrics;
import io.micrometer.core.instrument.Tags;
import lombok.extern.slf4j.Slf4j;
import reactor.core.observability.micrometer.Micrometer;
import reactor.core.scheduler.Scheduler;
import reactor.core.scheduler.Schedulers;
import org.jetbrains.annotations.NotNull;

import java.util.concurrent.ThreadFactory;
import java.util.function.Supplier;

/**
 * Metrics监控配置
 */
@Slf4j
public class MetricsHelper {

    /**
     * 启用Scheduler监控
     */
    public static void enableSchedulerMetrics() {
        Schedulers.setFactory(new SchedulersFactory(Metrics.globalRegistry));
    }

    private record SchedulersFactory(MeterRegistry registry) implements Schedulers.Factory {

        @NotNull
        @Override
        public Scheduler newBoundedElastic(int threadCap, int queuedTaskCap, @NotNull ThreadFactory threadFactory, int ttlSeconds) {
            String schedulerName = getSchedulerName(threadFactory);
            log.info("trigger newBoundedElastic, name:{}, threadCap:{}, queuedTaskCap:{}, ttlSeconds:{}", schedulerName, threadCap, queuedTaskCap, ttlSeconds);
            Scheduler scheduler = Schedulers.Factory.super.newBoundedElastic(threadCap, queuedTaskCap, threadFactory, ttlSeconds);
            return Micrometer.timedScheduler(scheduler, registry, "dex", Tags.of("scheduler_name", schedulerName, "scheduler_type", "bounded_elastic"));
        }

        @NotNull
        @Override
        public Scheduler newThreadPerTaskBoundedElastic(int threadCap, int queuedTaskCap, @NotNull ThreadFactory threadFactory) {
            String schedulerName = getSchedulerName(threadFactory);
            log.info("trigger newThreadPerTaskBoundedElastic, name:{},threadCap:{}, queuedTaskCap:{}", schedulerName, threadCap, queuedTaskCap);
            Scheduler scheduler = Schedulers.Factory.super.newThreadPerTaskBoundedElastic(threadCap, queuedTaskCap, threadFactory);
            return Micrometer.timedScheduler(scheduler, registry, "dex", Tags.of("scheduler_name", schedulerName, "scheduler_type", "thread_per_task_bounded_elastic"));
        }

        @NotNull
        @Override
        public Scheduler newParallel(int parallelism, @NotNull ThreadFactory threadFactory) {
            String schedulerName = getSchedulerName(threadFactory);
            log.info("trigger newParallel, name:{},parallelism:{}", schedulerName, parallelism);
            Scheduler scheduler = Schedulers.Factory.super.newParallel(parallelism, threadFactory);
            return Micrometer.timedScheduler(scheduler, registry, "dex", Tags.of("scheduler_name", schedulerName, "scheduler_type", "parallel"));
        }

        @NotNull
        @Override
        public Scheduler newSingle(@NotNull ThreadFactory threadFactory) {
            String schedulerName = getSchedulerName(threadFactory);
            log.info("trigger newSingle, name:{}", schedulerName);
            Scheduler scheduler = Schedulers.Factory.super.newSingle(threadFactory);
            return Micrometer.timedScheduler(scheduler, registry, "dex", Tags.of("scheduler_name", schedulerName, "scheduler_type", "single"));
        }

        private static String getSchedulerName(ThreadFactory threadFactory) {
            if (threadFactory instanceof Supplier<?> schedulerNameSupplier) {
                return schedulerNameSupplier.get().toString();
            }
            return "default";
        }
    }
}
