package com.cmc.dex.common.constants;

import static reactor.netty.Metrics.DATA_SENT;

public class MetricConstants {

    public static final String EDGE_PREFIX = "cmc_edge_";

    public static final String PUBLISHER_PREFIX = "cmc_publisher_";

    // push-client
    public static final String CLIENT_MSG_SENT = "cmc_push_client" + DATA_SENT;

    // push-pub-lantency
    public static final String LATENCY_FROM_MSG_CREATED_TO_CONSUMED = PUBLISHER_PREFIX + "consume_latency";

    public static final String LATENCY_FROM_MSG_CONSUMED_TO_EMIT = PUBLISHER_PREFIX + "emit_latency";

    // push-edge-latency
    public static final String LATENCY_FROM_MSG_CONSUMED_TO_EDGE_RECEIVED = EDGE_PREFIX + "consume_publisher_latency";

    public static final String LATENCY_FROM_MSG_CONSUMED_TO_PUSH = EDGE_PREFIX + "push_latency";

    // public static final String LATENCY_FROM_MSG_CREATED_TO_EDGE_RECEIVED = EDGE_PREFIX
    // + "consume_kafka_latency";

    // data sent
    public static final String DATA_SENT_PER_TOPIC_PUBLISHER = PUBLISHER_PREFIX + "total_data_sent_per_topic";

    public static final String DATA_SENT_PER_TOPIC_EDGE = EDGE_PREFIX + "total_data_sent_per_topic";

    // connections
    public static final String EDGE_CONNECTIONS_ACTIVE = EDGE_PREFIX + "websocket.connections.active.total";

    public static final String EDGE_CONNECTIONS_COUNT = EDGE_PREFIX + "websocket.connections.count";

    public static final String EDGE_CONNECTIONS_REGISTER = EDGE_PREFIX + "websocket.connections.register.total";

    public static final String SUBSCRIBERS_PER_TOPIC = EDGE_PREFIX + "subscribers_per_topic";

    public static final String TOPICS_PER_EDGE = EDGE_PREFIX + "topics_total";

    public static final String TOPICS_PER_PUBLISHER = PUBLISHER_PREFIX + "topics_per_publisher";
    public static final String SUBSCRIBED_TOPICS_PER_PUBLISHER = PUBLISHER_PREFIX + "subscribed_topics_per_publisher";
    public static final String TOPICS_EVICTION_PER_PUBLISHER = PUBLISHER_PREFIX + "topics_eviction_per_publisher";
    public static final String SINK_CONTEXT_PER_PUBLISHER = PUBLISHER_PREFIX + "sink_context_per_publisher";

    public static final String PUB_BLACKLIST_TRIGGERED_COUNTER = PUBLISHER_PREFIX + "blacklist_triggered";

    public static final String EDGE_BLACKLIST_TRIGGERED_COUNTER = EDGE_PREFIX + "blacklist_triggered";

    public static final String EDGE_WHITELIST_NOT_VALID_COUNTER = EDGE_PREFIX + "whitelist_not_valid";

    public static final String EDGE_WHITELIST_SIZE = EDGE_PREFIX + "whitelist_size";

    public static final String EDGE_AUTH = EDGE_PREFIX + "auth";

    public static final String PUBLISHER_TENANT_MSG = PUBLISHER_PREFIX + "tenant_msg";

    public static final String PUBLISHER_OVERFLOW = PUBLISHER_PREFIX + "overflow";

    // safeEmit result
    public static final String CLIENT_EMIT_KAFKA_FAIL = "cmc_client_emit_kafka_fail";

    public static final String EDGE_EMIT_WEBSOCKET_FAIL = EDGE_PREFIX + "emit_websocket_fail";

    public static final String EDGE_EMIT_RSOCKET_FAIL = EDGE_PREFIX + "emit_rsocket_fail";

    public static final String EDGE_EMIT_GROUP_SINK_FAIL = EDGE_PREFIX + "emit_group_sink_fail";

    public static final String EDGE_EMIT_DOWNSTREAM_FAIL = EDGE_PREFIX + "emit_downstream_fail";

    public static final String EDGE_EMIT_COMMAND_SINK_FAIL = EDGE_PREFIX + "emit_command_sink_fail";

    public static final String PUB_EMIT_RSOCKET_FAIL = PUBLISHER_PREFIX + "emit_rsocket_fail";

    public static final String PUB_EMIT_DOWNSTREAM_FAIL = PUBLISHER_PREFIX + "emit_downstream_fail";

    public static final String EDGE_WEBSOCKET_DEVICE_TYPE = EDGE_PREFIX + "device_type";

    public static final String EDGE_WEBSOCKET_CLIENT_SOURCE = EDGE_PREFIX + "client_source";

    public static final String EDGE_WEBSOCKET_TOTAL_CONNECTION = EDGE_PREFIX + "total_connection";

    public static final String EDGE_WEBSOCKET_NEW_SUBGROUP_COUNTER = EDGE_PREFIX + "websocket_new_subgroup";

    public static final String EDGE_WEBSOCKET_COMMAND_STATUS_CODE = EDGE_PREFIX + "command_status_code";

    public static final String EDGE_WEBSOCKET_COMMAND_REQUEST = EDGE_PREFIX + "command_request";

    public static final String TAG_IP = "ip_address";
    public static final String EVICTION_CAUSE = "cause";

    public static final String TAG_EDGE_CHANNEL = "edge";

    public static final String TAG_TOPIC = "topic";

    public static final String TAG_PRIVATE_ID = "private_id";

    public static final String TAG_TENANT = "tenant";

    public static final String TAG_PARTITION = "partition";

    public static final String TOPIC_SUFFIX = "_topic";

    public static final String EDGE_EMIT_DOWNSTREAM_ERROR = EDGE_PREFIX + "emit.downstream.error";

    public static final int SAMPLE_IN = 1;

    public static final int SAMPLE_OUT = 0;

    public static final String EDGE_CONNECTION_STATE = EDGE_PREFIX + "connection_state";

    public static final String SEND_TASK_TIME_COST = EDGE_PREFIX + "send_task_time_cost";
    public static final String EDGE_PUBLISHER_COUNT = EDGE_PREFIX + "publisher_connection_count";

    public static final String CMC_BASE_DATA_API_CALLS = "cmc.base-data.api.calls";
    public static final String CMC_BASE_DATA_API_ERRORS = "cmc.base-data.api.errors";
    public static final String TAG_API_NAME = "api_name";
    public static final String TAG_HTTP_STATUS = "http_status";
    public static final String CMC_BLOCKCHAIN_DATA_SERVICE_API_CALLS = "cmc.blockchain-data-service.api.calls";
    public static final String CMC_BLOCKCHAIN_DATA_SERVICE_API_ERRORS = "cmc.blockchain-data-service.api.errors";

    public static final String CMC_KLINE_SAVE_DDB_EXP = "cmc.kline.save.ddb.exp";
    public static final String CMC_KLINE_SAVE_DDB_ERRORS = "cmc.kline.save.ddb.errors";
    public static final String CMC_KLINE_SAVE_DDB_SUCCESS = "cmc.kline.save.ddb.success";
    public static final String CMC_KLINE_SAVE_DDB_UNSUCCESS = "cmc.kline.save.ddb.unsuccess";
    public static final String DDB_TABLE = "ddb_table";
    public static final String CMC_KLINE_SAVE_DDB_EXECUTION_TIME = "cmc.kline.save.ddb.execution.time";
    public static final String ERROR_METHOD = "BATCH_SAVE_ERRORS";
    public static final String DDB_BATCH_SAVE = "batch_save";
    public static final String KLINE_SAVE_DDB = "kline_save_ddb";
    public static final String KLINE_SEND_WS = "kline_send_ws";
    public static final String KLINE_SEND_MSG = "kline_send_msg";
    public static final String CMC_KLINE_CONSUMER_SAVE_DDB_COUNT = "cmc.kline.consumer.save.ddb.count";
    public static final String CMC_KLINE_CONSUMER_SAVE_DDD_TIME = "cmc.kline.consumer.save.ddb.time";
    public static final String CMC_KLINE_CONSUMER_SEND_WS_TIME = "cmc.kline.consumer.send.ws.time";
    public static final String CMC_KLINE_CONSUMER_SEND_MSG_TIME = "cmc.kline.consumer.send.msg.time";
    public static final String CMC_KLINE_CONSUMER_TOTAL_TIME = "cmc.kline.consumer.total.time";
    public static final String KLINE_CONSUME = "kline_consume";

    public static final  String KAFKA_CONSUME_COUNT = "nft.kafka.consume.count";

    public static final  String KAFKA_CONSUME_DELAY = "nft.kafka.consumer.delay";

    public static final String CMC_WEBSOCKET_SEND_COUNT = "cmc.websocket.send.count";

    public static final String CMC_WEBSOCKET_SEND_ERRORS = "cmc.websocket.send.errors";

    public static final  String COMMON_KAFKA_CONSUME_DELAY = "dex-kafka.consumer.delay";

    public static final  String COMMON_KAFKA_CONSUME_COUNT = "dex.kafka.consume.count";
    public static final String CACHE_LAST_UPDATE_TIME_SUFFIX = "aws_s3_local_cache_last_update_time";
    public static final String CACHE_SIZE_SUFFIX = "aws_s3_local_cache_size";
    public static final String CACHE_UPDATES_SUFFIX = "aws_s3_local_cache_updates";

    public static final String HISTOGRAM_SEARCH_DB_QUERY_TIME = "dex.search.list.db.query.time";
    public static final String HISTOGRAM_SEARCH_ES_QUERY_TIME = "dex.search.list.es.query.time";
    public static final String HISTOGRAM_SWAP_LIST_DB_QUERY_TIME = "dex.history.swap.list.db.query.time";
    public static final String HISTOGRAM_LIQUIDITY_CHANGE_LIST_DB_QUERY_TIME = "dex.history.liquidity.list.db.query.time";

    public static final String CMC_DQUERY_JOB_HANDLER_ERRORS = "cmc.dquery-job.handle.errors";

    public static final String TAG_JOB_NAME = "job_name";

}
