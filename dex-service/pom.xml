<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
  <modelVersion>4.0.0</modelVersion>

  <parent>
    <groupId>com.cmc</groupId>
    <artifactId>dquery-dex-service</artifactId>
    <version>1.0.40-SNAPSHOT</version>
  </parent>

  <groupId>com.cmc</groupId>
  <artifactId>dex-service</artifactId>
  <version>1.0.40-SNAPSHOT</version>

  <dependencies>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-model</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-common</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-business</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>dex-dao</artifactId>
      <exclusions>
        <exclusion>
          <artifactId>commons-logging</artifactId>
          <groupId>commons-logging</groupId>
        </exclusion>
      </exclusions>
    </dependency>

    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-apollo</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-webflux</artifactId>
    </dependency>

    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-boot-starter-webflux</artifactId>
    </dependency>

    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>dynamic-datasource-spring-boot3-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-boot-starter-mongodb-reactive</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-boot-starter-redis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-common</artifactId>
    </dependency>
    <dependency>
      <groupId>mysql</groupId>
      <artifactId>mysql-connector-java</artifactId>
    </dependency>
    <dependency>
      <groupId>org.mybatis</groupId>
      <artifactId>mybatis</artifactId>
    </dependency>
    <dependency>
      <groupId>com.baomidou</groupId>
      <artifactId>mybatis-plus-spring-boot3-starter</artifactId>
    </dependency>

    <dependency>
      <groupId>jakarta.annotation</groupId>
      <artifactId>jakarta.annotation-api</artifactId>
    </dependency>

    <dependency>
      <groupId>org.springframework.cloud</groupId>
      <artifactId>spring-cloud-starter-kubernetes-fabric8</artifactId>
    </dependency>
    <dependency>
      <groupId>com.cmc</groupId>
      <artifactId>cmc-framework-boot-starter-metrics</artifactId>
    </dependency>
    <dependency>
      <groupId>org.testng</groupId>
      <artifactId>testng</artifactId>
    </dependency>
    <dependency>
      <groupId>org.springframework.boot</groupId>
      <artifactId>spring-boot-test</artifactId>
    </dependency>
    <dependency>
    <groupId>org.mockito</groupId>
    <artifactId>mockito-core</artifactId>
    </dependency>

    <dependency>
      <groupId>com.github.luben</groupId>
      <artifactId>zstd-jni</artifactId>
      <version>1.5.7-1</version>
    </dependency>
  </dependencies>

  <build>
    <plugins>
      <plugin>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-maven-plugin</artifactId>
        <configuration>
          <finalName>dquery-dex-service</finalName>
        </configuration>
        <executions>
          <execution>
            <goals>
              <goal>repackage</goal>
            </goals>
          </execution>
        </executions>
      </plugin>
      <plugin>
        <groupId>com.spotify</groupId>
        <artifactId>dockerfile-maven-plugin</artifactId>
        <configuration>
          <!--
          TODO: the entire path should match corresponding path in the skaffold.yaml file.
          For example, if your image tag in skaffold.yaml is 346764516239.dkr.ecr.us-east-1.amazonaws.com/cmc-sample-service
          Then you will need to have docker.root.repo = 346764516239.dkr.ecr.us-east-1.amazonaws.com
          and ${docker.root.repo}/cmc-sample-service in this <repository> tag.
           -->
          <repository>${docker.root.repo}/dquery-dex-service</repository>
        </configuration>
      </plugin>
      <plugin>
        <groupId>org.apache.maven.plugins</groupId>
        <artifactId>maven-surefire-plugin</artifactId>
      </plugin>
      <plugin>
        <groupId>org.jacoco</groupId>
        <artifactId>jacoco-maven-plugin</artifactId>
      </plugin>
    </plugins>
  </build>
</project>