package com.cmc.dex.service;

import com.cmc.dex.common.util.MetricsHelper;
import com.cmc.framework.redis.annotation.EnableRedisConfiguration;
import com.cmc.framework.redis.annotation.EnableRedisConfiguration.RedisNamespace;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.actuate.autoconfigure.elasticsearch.ElasticsearchRestHealthContributorAutoConfiguration;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.data.mongo.MongoReactiveDataAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoAutoConfiguration;
import org.springframework.boot.autoconfigure.mongo.MongoReactiveAutoConfiguration;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.web.reactive.config.EnableWebFlux;

@SpringBootApplication(scanBasePackages = {"com.cmc.dex.*", "com.cmc.auth.common.utils"},
        exclude = {MongoAutoConfiguration.class, MongoReactiveAutoConfiguration.class, MongoReactiveDataAutoConfiguration.class,
                ElasticsearchRestHealthContributorAutoConfiguration.class})
@MapperScan(basePackages = {"com.cmc.dex.dao.repository.tidb", "com.cmc.dex.dao.repository.mysql"})
@EnableDiscoveryClient

@EnableRedisConfiguration({@RedisNamespace("backend.redis.dexer"),
        @RedisNamespace(value = "backend.redis.bigdata.blockchain",reactiveRedisTemplate = "bigDataRedisTemplate")})
@EnableWebFlux
public class DexServiceInitializer {

    public static void main(String[] args) {
        MetricsHelper.enableSchedulerMetrics();
        SpringApplication.run(DexServiceInitializer.class, args);
    }
}
