package com.cmc.dex.service.controller;

import com.cmc.dex.business.service.meme.DexMigrateService;
import com.cmc.dex.business.service.meme.MemeService;
import com.cmc.dex.model.meme.MemeCoinRequestDTO;
import com.cmc.dex.model.meme.MemeCoinResponseDTO;
import com.cmc.dex.model.token.MigrateCountDTO;
import com.cmc.framework.common.ApiResponse;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;

/**
 * Controller for meme coin related operations
 *
 * <AUTHOR>
 * @date 2025/5/23
 */
@RestController
@RequestMapping("/v1")
@Slf4j
@ApiResponse
@Tag(name = "meme-controller", description = "API for meme coin related operations")
@RequiredArgsConstructor
public class MemeController {

    private final MemeService memeService;

    private final DexMigrateService dexMigrateService;

    @Operation(summary = "Get meme coins", description = "Retrieve a list of meme coins based on the provided criteria")
    @PostMapping("/meme/list")
    public Mono<MemeCoinResponseDTO> getMemeCoins(@Parameter(
        description = "Request parameters for meme coin query") @Valid @RequestBody MemeCoinRequestDTO request) {
        log.debug("Received meme coin request: {}", request);
        return memeService.queryMemeCoins(request);
    }


    @GetMapping("/migration/count")
    public Mono<MigrateCountDTO> getTokenInfo(@RequestParam Integer platformId, @RequestParam String address) {
        return dexMigrateService.getMigrateCount(platformId, address);
    }
}
