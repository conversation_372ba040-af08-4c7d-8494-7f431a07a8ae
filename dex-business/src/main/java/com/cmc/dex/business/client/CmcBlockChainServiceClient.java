package com.cmc.dex.business.client;

import com.cmc.dex.business.client.model.TokenExtraInfoRequestDTO;
import com.cmc.dex.business.client.model.TokenExtraInfoResponse;
import com.cmc.dex.common.constants.MetricConstants;
import com.cmc.framework.http.HttpWebClient;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;
import reactor.util.retry.RetryBackoffSpec;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;

import java.io.IOException;
import java.time.Duration;
import java.util.function.Function;

/**
 * <AUTHOR>
 * @description query data from cmc website
 * @since 2025-05-22
 */
@Component
@Slf4j
public class CmcBlockChainServiceClient {

    private static final String ETH_TOKEN_EXTRA_INFO = "%s/eth/token/info/extra";

    @Value("${cmc.blockchain-data-service.url:https://api.beta.coinmarketcap.supply/blockchain-data-service}")
    private String url;

    @Value("${cmc.blockchain-data-service.api.retry.max-attempts:3}")
    private long retryMaxAttempts;

    @Value("${cmc.blockchain-data-service.api.retry.min-backoff:200}")
    private long retryMinBackoff;

    @Resource
    @Qualifier("defaultHttpClient")
    protected HttpWebClient httpWebClient;

    @Resource
    private CmcMeterRegistry cmcMeterRegistry;

    public Mono<ResponseEntity<TokenExtraInfoResponse>> getTokenExtraInfo(TokenExtraInfoRequestDTO requestDTO) {
        if (requestDTO == null || CollectionUtils.isEmpty(requestDTO.getTokens())) {
            return Mono.empty();
        }
        String tokenExtraInfoApi = String.format(ETH_TOKEN_EXTRA_INFO, url);
        return doPost(tokenExtraInfoApi, "getTokenExtraInfo", requestDTO, r -> {
            TokenExtraInfoResponse response = JacksonUtils.deserialize(r.getBody(), TokenExtraInfoResponse.class);
            return new ResponseEntity<>(response, r.getStatusCode());
        });
    }

    public <T> Mono<ResponseEntity<T>> doPost(String url, String apiName, Object body,
        Function<ResponseEntity<String>, ResponseEntity<T>> convertFunction) {
        return httpWebClient.post(url, body)
            .doFirst(() -> cmcMeterRegistry.counter(MetricConstants.CMC_BLOCKCHAIN_DATA_SERVICE_API_CALLS, MetricConstants.TAG_API_NAME, apiName))
            .doOnError(e -> {
                log.error("{} error", apiName, e);
                cmcMeterRegistry.counter(MetricConstants.CMC_BLOCKCHAIN_DATA_SERVICE_API_ERRORS, MetricConstants.TAG_API_NAME, apiName);
            })
            .doOnSuccess(response -> {
                if (!response.getStatusCode().is2xxSuccessful()) {
                    cmcMeterRegistry.counter(MetricConstants.CMC_BLOCKCHAIN_DATA_SERVICE_API_ERRORS, MetricConstants.TAG_API_NAME, apiName, MetricConstants.TAG_HTTP_STATUS, response.getStatusCode().value() + "");
                }
            })
            .retryWhen(getRetrySpec())
            .filter(r -> StringUtils.isNotBlank(r.getBody()))
            .map(convertFunction);
    }

    private @NotNull RetryBackoffSpec getRetrySpec() {
        return Retry.backoff(retryMaxAttempts, Duration.ofMillis(retryMinBackoff)).filter(e -> {
            if (e instanceof WebClientResponseException) {
                int statusCode = ((WebClientResponseException)e).getStatusCode().value();
                // 4xx or 5xx retry
                return statusCode >= 400 && statusCode < 600;
            }
            // IOException retry
            return e instanceof IOException;
        }).doAfterRetry(retrySignal -> log.error("Retry after error", retrySignal.failure()));
    }
}