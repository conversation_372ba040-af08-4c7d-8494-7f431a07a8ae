package com.cmc.dex.business.service.token;

import com.cmc.dex.dao.entity.tidb.DexPoolDetailEntity;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.model.enums.EventTypeEnum;
import com.cmc.dex.model.token.BatchPlatformTokenRequestDTO;
import com.cmc.dex.model.token.TokenDetailDTO;
import com.cmc.dex.model.token.TokenPriceDTO;
import com.cmc.dex.model.token.TokenTopPoolDTO;
import jakarta.validation.Valid;
import reactor.core.publisher.Mono;
import reactor.util.function.Tuple2;
import org.jetbrains.annotations.NotNull;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.Collection;
import java.util.List;



/**
 * <AUTHOR>
 */
public interface TokenService extends IService<DexTokenDetailEntity> {

    /**
     * Get token detail DTO from cache or database.
     *
     * @param tokenAddress the token address
     * @param platform     the platform id
     * @return the token detail
     */
    Mono<TokenDetailDTO> getTokenDetailDTO(String platform, String tokenAddress);

    /**
     * Get token price info.
     *
     * @param platform     the platform id
     * @param tokenAddress the token address
     * @return the token price info
     */
    Mono<TokenPriceDTO> getTokenPrice(String platform, String tokenAddress);

    Mono<List<TokenPriceDTO>> batchGetTokenPrice(@Valid BatchPlatformTokenRequestDTO request);

    /**
     * Get token top pools.
     *
     * @param platform
     * @param address
     * @param size
     * @return
     */
    Mono<List<TokenTopPoolDTO>> getTokenTopPools(String platform, String address, Integer size);


    Mono<DexTokenDetailEntity> getTokenDetailEntity(Integer platformId, String tokenAddress);

    Mono<Collection<DexTokenDetailEntity>> getTokenDetailEntities(List<Tuple2<Integer, String>> platformAndAddressList);

    /**
     * batch get token detail DTO from cache or database.
     *
     * @param platform
     * @param addresses
     * @return
     */
    Mono<List<TokenDetailDTO>> batchGetTokensDTO(String platform, List<String> addresses);

    /**
     * batch get token detail entity from cache or database.
     *
     * @param platform
     * @param addresses
     * @return
     */
    Mono<List<DexTokenDetailEntity>> batchGetTokenDetailEntity(Integer platform, Collection<String> addresses);


    /**
     * Query tokens from database based on keyword and platform, and exclude blacklisted platformIds
     *
     * @param keyword    search keyword for address, name, or symbol prefix matching
     * @param platformId platform ID filter
     * @param sort       sort field name (e.g. priceUsd, liquidity)
     * @param order      sort direction (asc or desc)
     * @param limit      maximum number of results to return
     * @param blackPlatformIds platformId blacklist, will be excluded from result
     * @return list of token detail entities
     */
    Mono<List<DexTokenDetailEntity>> queryFromDB(String keyword, Integer platformId, String sort, String order, @NotNull Integer limit, List<Integer> blackPlatformIds);

    Integer getBaseTokenIndex(Integer platform, String token0, String token1);

    Mono<Boolean> batchRefreshCache(Integer platform, List<String> addresses);


    Mono<List<DexPoolDetailEntity>> queryTopPoolEntities(Integer platform, String address, Integer size);

    /**
     * Send dex token detail create/update event to kafka
     * TokenDetailEventConsumer will handle the event and persist to database
     * @param entities
     * @param eventEnum
     * @return
     */
    Mono<Void> sendDexTokenDetailEvent(List<DexTokenDetailEntity> entities, EventTypeEnum eventEnum);
}
