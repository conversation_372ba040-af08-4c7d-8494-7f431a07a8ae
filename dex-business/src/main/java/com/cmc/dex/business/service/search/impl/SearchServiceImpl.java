package com.cmc.dex.business.service.search.impl;

import static com.cmc.dex.common.constants.DataConstants.ENV_LOCAL;
import static com.cmc.dex.common.constants.DataConstants.ENV_PROPERTY_NAME;
import static com.cmc.framework.utils.StringUtils.isBlank;
import static com.cmc.framework.utils.StringUtils.isNotBlank;

import com.cmc.dex.business.mapstruct.ValueConverter;
import com.cmc.dex.business.service.platform.PlatformService;
import com.cmc.dex.business.service.search.SearchService;
import com.cmc.dex.business.service.token.TokenService;
import com.cmc.dex.common.constants.MetricConstants;
import com.cmc.dex.common.enums.ErrorCode;
import com.cmc.dex.common.util.IpfsUrlRewriterUtils;
import com.cmc.dex.dao.entity.es.TokenEsEntity;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.dao.repository.es.TokenEsRepository;
import com.cmc.dex.dao.repository.redis.TokenSearchRedisRepository;
import com.cmc.dex.model.platform.PlatformDTO;
import com.cmc.dex.model.search.SearchResponseDTO;
import com.cmc.dex.model.search.SearchResultDTO;
import com.cmc.framework.common.exception.ApplicationException;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import com.github.benmanes.caffeine.cache.AsyncCacheLoader;
import com.github.benmanes.caffeine.cache.AsyncLoadingCache;
import com.github.benmanes.caffeine.cache.Caffeine;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.Duration;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Executor;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;
import jakarta.annotation.PostConstruct;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * Implementation of SearchService for token search functionality
 */
@ConditionalOnProperty(name = "spring.application.name", havingValue = "dquery-dex-service")
@Service
@Slf4j
public class SearchServiceImpl implements SearchService {

    @Resource
    private TokenService tokenService;
    @Resource
    private TokenEsRepository tokenEsRepository;
    @Resource
    private TokenSearchRedisRepository tokenSearchRedisRepository;
    @Resource
    private PlatformService platformService;
    @Resource
    private ValueConverter valueConverter;
    @Resource
    private CmcMeterRegistry cmcMeterRegistry;
    @Resource
    @Qualifier("searchCacheScheduler")
    private Executor searchCacheScheduler;

    @Value("${com.cmc.dexer-service.search.keyword.max-length:100}")
    private int searchKeyWordMaxLength;
    @Value("${com.cmc.dexer-service.search.keyword.search-address-length:20}")
    private int searchAddressLength;
    @Value("${com.cmc.dexer-service.search.keyword.special-chars:}")
    private String specialChars;
    @Value("${com.cmc.dexer-service.search-filter-regex:[^A-Za-z0-9]}")
    private String filterRegex;

    @Value("${com.cmc.dquery-service.search.max-limit:100}")
    private Integer searchMaxLimit;
    @Value("${com.cmc.dquery-service.search.default-limit:50}")
    private Integer searchDefaultLimit;
    @Value("${com.cmc.dquery-service.search.limit-values:3,50,100}")
    private List<Integer> limitValueList;
    @Value("${com.cmc.dquery-service.search.cache-max-size:10000}")
    private Integer cacheMaxSize;
    @Value("${com.cmc.dquery-service.search.cache-refresh-mills:30000}")
    private Integer cacheRefreshMills;
    @Value("${com.cmc.dquery-service.search.cache-expired-mills:60000}")
    private Integer cacheExpiredMills;

    @Value("${com.cmc.dquery-service.search.redis-cache-expired-seconds:120}")
    private Integer redisCacheExpiredSeconds;
    @Value("${com.cmc.dquery-service.search.redis-cache-short-expired-seconds:10}")
    private Integer redisCacheShortExpiredSeconds;

    @Value("${com.cmc.dquery-service.search.hot-words.refresh-init-delay-ms:600}")
    private Long initRefreshDelaySeconds;
    @Value("${com.cmc.dquery-service.search.hot-words.refresh-init-letter-delay-ms:900}")
    private Long initLetterRefreshDelaySeconds;
    @Value("${com.cmc.dquery-service.search.hot-words.refresh-interval-ms:300000}")
    private Long intervalMs;
    @Value("${com.cmc.dquery-service.search.hot-words.refresh-interval-letter-ms:500000}")
    private Long intervalLetterMs;
    @Value("${com.cmc.dquery-service.search.hot-words.preload-platform-ids:1,16}")
    private List<Integer> preloadPlatformIds;
    @Value("${com.cmc.dquery-service.search.auto-reload-hot-items:100}")
    private Integer autoReloadHotItems;

    @Value("${com.cmc.dquery-service.search.enable-refresh:true}")
    private Boolean enableRefresh;

    @Value("${com.cmc.dquery-service.search.switch-query-es:true}")
    private boolean switchQueryEs;

    @Value("${com.cmc.dquery-service.search.address.must.query.length:10}")
    private int addressMustQueryLength;
    @Value("${com.cmc.dquery-service.search.symbol.must.query.length:3}")
    private int symbolMustQueryLength;

    @Value("${com.cmc.dquery-service.search.black-platform-ids:}")
    private List<Integer> blackPlatformIds = new ArrayList<>();

    private AsyncLoadingCache<String, List<SearchResultDTO>> searchCache;

    @PostConstruct
    public void init() {
        this.searchCache = Caffeine.newBuilder()
            .executor(searchCacheScheduler)
            .maximumSize(cacheMaxSize)
            .refreshAfterWrite(Duration.ofMillis(cacheRefreshMills))
            .expireAfterAccess(Duration.ofMillis(cacheExpiredMills))
            .buildAsync(this.buildSearchCache());

        if (enableRefresh) {
            this.refreshHotWordsCache();

            Flux.interval(Duration.ofSeconds(initRefreshDelaySeconds), Duration.ofMillis(intervalMs))
                .publishOn(Schedulers.boundedElastic())
                .doOnNext(tick -> refreshHotWordsCache())
                .subscribe();

//            Flux.interval(Duration.ofSeconds(initLetterRefreshDelaySeconds), Duration.ofMillis(intervalLetterMs))
//                .publishOn(Schedulers.boundedElastic())
//                .doOnNext(tick -> refreshSingleLetterCache())
//                .subscribe();
        }
    }

    private AsyncCacheLoader<? super String, List<SearchResultDTO>> buildSearchCache() {
        return new AsyncCacheLoader<>() {
            @Override
            public CompletableFuture<List<SearchResultDTO>> asyncLoad(String key, Executor executor) {
                String[] parts = key.split(":");
                String keyword = URLDecoder.decode(parts[0], StandardCharsets.UTF_8);
                Integer platformId = !Objects.equals(parts[1], "") ? Integer.parseInt(parts[1]) : null;
                String sort = parts[2];

                return tokenSearchRedisRepository.getSearchResult(key)
                    .filter(StringUtils::isNotBlank)
                    .map(str -> JacksonUtils.deserialize(str, new TypeReference<List<SearchResultDTO>>() {
                    }))
                    .switchIfEmpty(Mono.defer(() -> loadDataAndCache(key, keyword, platformId, sort)))
                    .toFuture();
            }

            @Override
            public CompletableFuture<List<SearchResultDTO>> asyncReload(String key, List<SearchResultDTO> oldValue,
                Executor executor) {
                // Only query DB and update Redis, do not use Redis cache
                String[] parts = key.split(":");
                String keyword = URLDecoder.decode(parts[0], StandardCharsets.UTF_8);
                Integer platformId = !Objects.equals(parts[1], "") ? Integer.parseInt(parts[1]) : null;
                String sort = parts[2];
                return loadDataAndCache(key, keyword, platformId, sort).toFuture();
            }

            @NotNull
            private Mono<List<SearchResultDTO>> loadDataAndCache(String key, String keyword, Integer platformId,
                String sort) {
                Mono<List<SearchResultDTO>> listMono =
                    switchQueryEs ? queryFromES(keyword, platformId, sort) : queryFromTiDB(keyword, platformId, sort);
                return listMono.doOnNext(dbResults -> {
                    long expireSeconds = CollectionUtils.isNotEmpty(dbResults) ? redisCacheExpiredSeconds
                        : redisCacheShortExpiredSeconds;
                    tokenSearchRedisRepository.setSearchResult(key, JacksonUtils.serialize(dbResults), expireSeconds)
                        .subscribeOn(Schedulers.boundedElastic())
                        .subscribe();
                });
            }

            @NotNull
            private Mono<List<SearchResultDTO>> queryFromES(String keyword, Integer platformId, String sort) {
                Flux<TokenEsEntity> esEntityFlux;
                if (keyword.length() >= addressMustQueryLength) {
                    esEntityFlux = tokenEsRepository.findByAddress(keyword, platformId, blackPlatformIds,
                        PageRequest.of(0, searchMaxLimit, Sort.by(Sort.Direction.DESC, sort)));
                } else {
                    esEntityFlux = tokenEsRepository.findBySymbol(keyword, platformId, blackPlatformIds,
                        PageRequest.of(0, searchMaxLimit, Sort.by(Sort.Direction.DESC, sort)));
                }
                return esEntityFlux.collectList().subscribeOn(Schedulers.boundedElastic()).elapsed().map(tuple -> {
                    long duration = tuple.getT1();
                    cmcMeterRegistry.timer(MetricConstants.HISTOGRAM_SEARCH_ES_QUERY_TIME)
                        .record(duration, TimeUnit.MILLISECONDS);
                    return tuple.getT2();
                }).flatMap(SearchServiceImpl.this::convertToEsSearchResults);
            }

            @NotNull
            private Mono<List<SearchResultDTO>> queryFromTiDB(String keyword, Integer platformId, String sort) {
                return tokenService.queryFromDB(keyword, platformId, sort, "desc", searchMaxLimit, blackPlatformIds)
                    .subscribeOn(Schedulers.boundedElastic())
                    .elapsed()
                    .map(tuple -> {
                        long duration = tuple.getT1();
                        cmcMeterRegistry.timer(MetricConstants.HISTOGRAM_SEARCH_DB_QUERY_TIME)
                            .record(duration, TimeUnit.MILLISECONDS);
                        return tuple.getT2();
                    })
                    .map(SearchServiceImpl.this::convertToSearchResults);
            }

        };
    }

    public void refreshHotWordsCache() {
        if (ENV_LOCAL.equalsIgnoreCase(System.getenv(ENV_PROPERTY_NAME))) {
            return;
        }
        tokenSearchRedisRepository.getHotWords(autoReloadHotItems)
            .flatMapMany(hotWords -> Flux.fromIterable(hotWords).buffer(100))
            .flatMap(batch -> Mono.fromFuture(searchCache.getAll(batch)))
            .then()
            .doOnSuccess(e -> log.info("refreshHotWordsCache success"))
            .onErrorResume(e -> {
                log.error("refreshHotWordsCache error", e);
                return Mono.empty();
            })
            .subscribe();
    }

    public void refreshSingleLetterCache() {
        if (ENV_LOCAL.equalsIgnoreCase(System.getenv(ENV_PROPERTY_NAME))) {
            return;
        }
        List<String> letters =
            Arrays.asList("a", "b", "c", "d", "e", "f", "g", "h", "i", "j", "k", "l", "m", "n", "o", "p", "q", "r", "s",
                "t", "u", "v", "w", "x", "y", "z");
        List<String> sorts = List.of("volume_usd_24h", "liquidity_usd");

        List<String> cacheKeys = new ArrayList<>();
        for (String letter : letters) {
            String finalKeyword = URLEncoder.encode(letter, StandardCharsets.UTF_8);
            for (Integer platformId : preloadPlatformIds) {
                for (String sortKey : sorts) {
                    String cacheKey =
                        String.join(":", finalKeyword, platformId == null ? "" : platformId.toString(), sortKey);
                    cacheKeys.add(cacheKey);
                }
            }
        }

        Flux.fromIterable(cacheKeys)
            .buffer(100)
            .flatMap(batch -> Mono.fromFuture(searchCache.getAll(batch)))
            .then()
            .doOnSuccess(e -> log.info("refreshSingleLetterCache success"))
            .onErrorResume(e -> {
                log.error("refreshSingleLetterCache error", e);
                return Mono.empty();
            })
            .subscribe();
    }

    /**
     * Search for tokens based on query and optional platform
     *
     * @param keyword
     *     search query string
     * @param platform
     *     optional platform filter
     * @param sort
     *     optional sort field
     * @param limit
     *     optional result limit
     * @return list of search results
     */
    @Override
    public Mono<SearchResponseDTO> search(String keyword, String platform, String sort, Integer limit) {
        SearchResponseDTO emptyRes = SearchResponseDTO.builder().tokens(new ArrayList<>()).total(0).build();
        if (isNotBlank(keyword)) {
            if (keyword.length() > searchKeyWordMaxLength) {
                return Mono.just(emptyRes);
            }
            keyword = keyword.replaceAll(specialChars, "");
            if (isNotExist(keyword)) {
                return Mono.just(emptyRes);
            }
        }
        if (StringUtils.isBlank(keyword)) {
            return Mono.just(emptyRes);
        }

        String sortKey = "volume_usd_24h";
        if (StringUtils.equalsIgnoreCase("liquidity", sort)) {
            sortKey = "liquidity_usd";
        }
        Integer resLimit;
        if (limit != null && limitValueList.contains(limit)) {
            resLimit = limit;
        } else {
            resLimit = searchDefaultLimit;
        }

        Integer platformId = null;
        if (StringUtils.isNotBlank(platform)) {
            PlatformDTO platformDTO = platformService.getPlatformDetail(platform);
            if (platformDTO == null) {
                return Mono.error(new ApplicationException(ErrorCode.PARAM_VALID_ERROR));
            }
            platformId = platformDTO.getId();
        }

        String finalKeyword = URLEncoder.encode(keyword.trim().toLowerCase(), StandardCharsets.UTF_8);
        String cacheKey = String.join(":", finalKeyword, platformId == null ? "" : platformId.toString(), sortKey);
        return Mono.fromFuture(searchCache.get(cacheKey))
            .filter(CollectionUtils::isNotEmpty)
            .defaultIfEmpty(new ArrayList<>())
            .map(tokens -> {
                List<SearchResultDTO> resultDTOS =
                    tokens.stream().limit(resLimit).collect(Collectors.toList());
                return SearchResponseDTO.builder().tokens(resultDTOS).total(resultDTOS.size()).build();
            })
            .doOnNext(searchResultDTO -> {
                if (CollectionUtils.isNotEmpty(searchResultDTO.getTokens())) {
                    tokenSearchRedisRepository.addOrUpdateHotWord(cacheKey, System.currentTimeMillis())
                        .subscribeOn(Schedulers.boundedElastic())
                        .subscribe();
                }
            });
    }

    private boolean isNotExist(String keyword) {
        keyword = keyword.replaceAll(filterRegex, keyword);
        if (isBlank(keyword)) {
            return true;
        }
        if (keyword.length() > searchAddressLength) {
            return false;
        }
        return false;
    }

    /**
     * Convert DexTokenDetailEntity list to SearchResultDTO list
     *
     * @param entities
     *     list of token detail entities
     * @return list of search result DTOs
     */
    private List<SearchResultDTO> convertToSearchResults(List<DexTokenDetailEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return new ArrayList<>();
        }
        Map<Integer, PlatformDTO> platformMap = platformService.getPlatformMap();

        return entities.stream().map(valueConverter::convertToSearchResult).peek(dto -> {
            Integer platformId = dto.getPlatformId();
            if (platformId == null) {
                return;
            }
            PlatformDTO platformDTO = platformMap.get(platformId);
            if (platformDTO == null) {
                return;
            }
            dto.setPlatform(platformDTO.getDn());
            dto.setPlatformCryptoId(platformDTO.getCId());
            dto.setLogoUrl(IpfsUrlRewriterUtils.rewriteIpfsUrls(dto.getLogoUrl()));
        }).collect(Collectors.toList());
    }

    /**
     * Convert TokenEsEntity list to SearchResultDTO list
     *
     * @param entities
     *     list of token es entities
     * @return list of search result DTOs
     */
    private Mono<List<SearchResultDTO>> convertToEsSearchResults(List<TokenEsEntity> entities) {
        if (entities == null || entities.isEmpty()) {
            return Mono.just(List.of());
        }
        Map<Integer, PlatformDTO> platformMap = platformService.getPlatformMap();
        tokenService.getTokenDetailEntities()

        return entities.stream().peek(entity -> {
            Integer platformId = dto.getPlatformId();
            if (platformId == null) {
                return;
            }
            PlatformDTO platformDTO = platformMap.get(platformId);
            if (platformDTO == null) {
                return;
            }
//            dto.setPlatform(platformDTO.getDn());
//            dto.setPlatformCryptoId(platformDTO.getCId());
//            dto.setLogoUrl(IpfsUrlRewriterUtils.rewriteIpfsUrls(dto.getLogoUrl()));
            return SearchResultDTO.builder().build();
        }).collect(Collectors.toList());
    }

}