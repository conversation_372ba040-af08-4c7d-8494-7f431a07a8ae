package com.cmc.dex.business.service.token.impl;

import static com.cmc.dex.common.constants.DataConstants.SOLANA_PLATFORM_ID;
import static com.cmc.dex.common.constants.RedisConstants.TOKEN_SECURITY_SYNC_INTERVAL_KEY;
import static com.cmc.dex.common.constants.RedisConstants.TOKEN_SECURITY_SYNC_KEY;
import static com.cmc.dex.common.util.DexSchedulers.asyncExecuteWithSubscribe;
import static java.util.stream.Collectors.toSet;

import com.cmc.dex.business.client.BinanceClient;
import com.cmc.dex.business.converter.impl.TokenSecurityEntityToOriginalDtoConverter;
import com.cmc.dex.business.converter.impl.TokenSecurityParsedDtoToEntityConverter;
import com.cmc.dex.business.converter.impl.TokenSecurityToDisplayDtoConverter;
import com.cmc.dex.business.enums.TokenRefreshEventTypeEnum;
import com.cmc.dex.business.service.platform.PlatformService;
import com.cmc.dex.business.service.token.TokenSecurityService;
import com.cmc.dex.business.service.token.TokenService;
import com.cmc.dex.common.constants.DataConstants;
import com.cmc.dex.dao.entity.mongodb.audit.TokenSecurityEntity;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.dao.repository.mongodb.dexer.TokenSecurityMongoRepository;
import com.cmc.dex.dao.repository.redis.RedisConstantKey;
import com.cmc.dex.dao.repository.redis.TokenSecurityRepository;
import com.cmc.dex.model.constant.TokenSecurityConstants;
import com.cmc.dex.model.enums.DisplayRiskLevelStatusEnum;
import com.cmc.dex.model.enums.EventTypeEnum;
import com.cmc.dex.model.enums.SecurityRiskLevelEnum;
import com.cmc.dex.model.kafka.TokenExtraEventDTO;
import com.cmc.dex.model.kafka.TokenInfoDTO;
import com.cmc.dex.model.platform.PlatformDTO;
import com.cmc.dex.model.security.ChainDTO;
import com.cmc.dex.model.security.TokenSecurityDTO;
import com.cmc.dex.model.security.TokenSecurityOriginalDTO;
import com.cmc.dex.model.security.TokenSecurityResponseDTO;
import com.cmc.framework.kafka.client.KafkaProducer;
import com.cmc.framework.kafka.client.ProducerMessage;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.google.common.collect.Lists;
import java.time.Duration;
import java.util.Collection;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.apache.commons.collections4.ListUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * @Description Token Security
 * <AUTHOR> Z
 * @CreateTime 2025-05-22
 */
@Slf4j
@Service
public class TokenSecurityServiceImpl implements TokenSecurityService {

    @ApolloJsonValue("${binance.cmc-binance.chain.map:}")
    private List<ChainDTO> chainRefDTOS;
    @Value("${com.cmc.dexer.binance.security.business:cmc_token_audit}")
    private String bscSecurityBusiness;
    @Value("${com.cmc.dexer.binance.security.refresh-interval.second:600}")
    private Integer securityRefreshInterval;
    @Value("${com.cmc.dexer.binance.security.original.cache.expire.second:86400}")
    private Long securityCacheExpire;
    @Value("${dexer.token.refresh.topic:dex_token_risk_level_refresh}")
    private String tokenRefreshTopic;
    @ApolloJsonValue("${dexer.token.security.binance.code-ref:}")
    private Map<String, List<String>> binanceCodeRefMap;
    @Value("${dexer.token.security.redis.cache.refresh:60000}")
    private int dataRefreshTime;

    @Autowired
    private BinanceClient binanceClient;
    @Autowired
    private PlatformService platformService;
    @Autowired
    private TokenSecurityMongoRepository tokenSecurityMongoRepository;
    @Autowired
    private TokenSecurityRepository tokenSecurityRepository;
    @Autowired
    private TokenSecurityParsedDtoToEntityConverter tokenSecurityParsedDtoToEntityConverter;
    @Autowired
    private TokenSecurityEntityToOriginalDtoConverter tokenSecurityEntityToOriginalDtoConverter;
    @Autowired
    private TokenSecurityToDisplayDtoConverter tokenSecurityToDisplayDtoConverter;
    @Autowired
    private TokenService tokenService;

    @Autowired
    @Qualifier("indexer")
    private KafkaProducer kafkaProducer;

    @Override
    public Mono<List<TokenSecurityEntity>> batchRefreshFromThirdParty(Integer platformId, Collection<String> tokenAds,
        Boolean timeInterval, String businessType) {
        if(CollectionUtils.isEmpty(tokenAds)){
            return Mono.just(Collections.emptyList());
        }
        return Flux.fromIterable(tokenAds)
            .filterWhen(tokenCa -> {
                log.debug("Token security batch refresh filter tokenCa {}, platformId {}", tokenCa, platformId);
                if(Boolean.TRUE.equals(timeInterval) && securityRefreshInterval != null && securityRefreshInterval.compareTo(0) > 0){
                    String key = String.format(TOKEN_SECURITY_SYNC_INTERVAL_KEY, platformId.toString(), tokenCa);
                    return tokenSecurityRepository.setIfAbsent(key, Duration.ofSeconds(securityRefreshInterval));
                }
                return Mono.just(true);
            }).collectList()
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(caList -> updateTokenSecurity(platformId, caList, businessType)
                .flatMapMany(Flux::fromIterable)
                .filter(securityEntity -> securityEntity.getIsSupported() == null ||
                    Boolean.TRUE.equals(securityEntity.getIsSupported()) ||
                    (securityEntity.getSecurityLevel() != null && securityEntity.getSecurityLevel() > 0))
                .collectList()
                .filter(CollectionUtils::isNotEmpty)
                .doOnNext(entities -> asyncExecuteWithSubscribe(() -> updateTokenDetailLevel(entities))))
            .filter(CollectionUtils::isNotEmpty)
            .doOnNext(entities -> asyncExecuteWithSubscribe(() -> updateCache(entities)))
            .doOnError(e -> log.error("Token security refresh error. platformId {}, tokenCaList {}", platformId, tokenAds, e));
    }

    @Override
    public Mono<List<TokenSecurityOriginalDTO>> queryTokenSecurityOriginal(Integer platformId, Collection<String> tokenAds, Boolean refresh) {
        if(Boolean.TRUE.equals(refresh)){
            return batchRefreshFromThirdParty(platformId, tokenAds, false, StringUtils.EMPTY)
                .map(tokenSecurityEntityToOriginalDtoConverter::convertList);
        }
        return tokenSecurityRepository.batchGet(platformId, tokenAds)
            .defaultIfEmpty(Collections.emptyList())
            .flatMap(found -> {
                Collection<String> missing;
                if (CollectionUtils.isEmpty(found)) {
                    missing = tokenAds;
                } else if (found.size() == tokenAds.size()) {
                    return Mono.justOrEmpty(found);
                } else {
                    Set<String> foundAddr = found.stream()
                        .filter(d -> StringUtils.isNotBlank(d.getAddress()))
                        .map(d -> d.getAddress().toLowerCase())
                        .collect(toSet());
                    missing = tokenAds.stream()
                        .filter(t -> !foundAddr.contains(t.toLowerCase()))
                        .collect(toSet());
                }
                return putCache(platformId, missing)
                    .map(t -> ListUtils.union(found, t));
            }).map(resultList -> resultList.stream()
                .filter(d -> StringUtils.isNotBlank(d.getAddress()))
                .collect(Collectors.toList()))
            .doOnNext(r -> asyncExecuteWithSubscribe(() -> sendTokenSecurityKafka(platformId, tokenAds)));
    }

    @Override
    public Mono<TokenSecurityOriginalDTO> getTokenSecurityOriginal(Integer platformId, String tokenAd, Boolean refresh) {
        if(Boolean.TRUE.equals(refresh)){
            return batchRefreshFromThirdParty(platformId, List.of(tokenAd), false, StringUtils.EMPTY)
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> tokenSecurityEntityToOriginalDtoConverter.convert(list.get(0)));
        }
        String cacheKey = RedisConstantKey.TOKEN_SECURITY_ORIGINAL_CACHE_KEY.apply(platformId, tokenAd);
        return tokenSecurityRepository.get(cacheKey, TokenSecurityOriginalDTO.class)
            .switchIfEmpty(Mono.defer(() -> fromDB(platformId, Set.of(tokenAd)))
                .filter(CollectionUtils::isNotEmpty)
                .map(list -> list.get(0))
                .switchIfEmpty(Mono.defer(() -> Mono.just(buildDefaultDto(platformId, tokenAd)))))
            .doOnNext(dto -> {
                if(!needRefresh(dto)){
                    return;
                }

                if(dto.getDataTimestamp() == null && CollectionUtils.isNotEmpty(dto.getSecurityItems())){
                    dto.setDataTimestamp(System.currentTimeMillis());
                    asyncExecuteWithSubscribe(() -> tokenSecurityRepository.set(cacheKey, JacksonUtils.toJson(dto), securityCacheExpire)
                        .flatMap(r -> sendTokenSecurityKafka(platformId, Set.of(tokenAd))));
                }else {
                    asyncExecuteWithSubscribe(() -> {
                        String rLockKey = DataConstants.JOIN_STRING_FUNCTION.apply(List.of(cacheKey, "rLock"));
                        return tokenSecurityRepository.setIfAbsent(rLockKey, cacheKey, 3L)
                            .filter(Boolean::booleanValue)
                            .flatMap(lockResult -> fromDB(platformId, Set.of(tokenAd)))
                            .filter(CollectionUtils::isNotEmpty)
                            .flatMap(originalDTOS -> {
                                TokenSecurityOriginalDTO dto1 = originalDTOS.get(0);
                                dto1.setDataTimestamp(System.currentTimeMillis());
                                return tokenSecurityRepository.set(cacheKey, JacksonUtils.toJson(dto1), securityCacheExpire)
                                    .flatMap(r -> sendTokenSecurityKafka(platformId, Set.of(tokenAd)));
                            }).doFinally(tmp -> tokenSecurityRepository.delete(rLockKey).subscribe());
                    });
                }
            });
    }

    @Override
    public Mono<List<TokenSecurityDTO>> queryTokenSecurity(Integer platformId, Collection<String> tokenAds, Boolean refresh) {
        return queryTokenSecurityOriginal(platformId, tokenAds, refresh)
            .map(tokenSecurityToDisplayDtoConverter::convertList);
    }

    @Override
    public Mono<List<TokenSecurityResponseDTO>> queryTokenSecurityPageList(String platformName, Collection<String> tokenAds) {
        return platformService.getDetail(platformName)
            .filter(p -> p.getId() != null)
            .flatMap(p -> {
                Mono<List<TokenSecurityOriginalDTO>> resultMono;
                if(tokenAds.size() == 1){
                    resultMono = getTokenSecurityOriginal(p.getId(), tokenAds.stream().toList().get(0), false)
                        .map(dto -> Lists.newArrayList(dto));
                }else {
                    resultMono = queryTokenSecurityOriginal(p.getId(), tokenAds, false);
                }

                return resultMono.flatMapMany(Flux::fromIterable)
                    .map(d -> buildTokenSecurityResponseDTO(p, d))
                    .collectList();
            });
    }

    @Override
    public Mono<Boolean> sendTokenSecurityKafka(Integer platformId, Collection<String> tokenAds){

        List<TokenInfoDTO> tokenInfoDTOS = tokenAds.stream()
            .map(ca -> TokenInfoDTO.builder()
                .platformId(platformId)
                .address(ca)
                .build()
            ).collect(Collectors.toList());

        String kafkaKey = platformId + "_" + tokenAds;
        TokenExtraEventDTO tokenExtraEventDTO = TokenExtraEventDTO.builder()
            .type(TokenRefreshEventTypeEnum.SECURITY.getType())
            .tokenInfoDTOS(tokenInfoDTOS)
            .sendTimestamp(System.currentTimeMillis())
            .build();

        ProducerMessage<String, String, Object>
            producerMessage = new ProducerMessage<>(kafkaKey, JacksonUtils.serialize(tokenExtraEventDTO), new Object());

        return kafkaProducer.send(tokenRefreshTopic, Mono.just(producerMessage)).thenReturn(Boolean.TRUE);
    }

    private Boolean needRefresh(TokenSecurityOriginalDTO originalDTO){
        return Optional.ofNullable(originalDTO.getDataTimestamp())
            .map(t -> System.currentTimeMillis() - t > dataRefreshTime)
            .orElse(Boolean.TRUE);
    }

    private TokenSecurityOriginalDTO buildDefaultDto(Integer platformId, String tokenAd){
        TokenSecurityOriginalDTO tokenSecurityOriginalDTO = new TokenSecurityOriginalDTO();
        tokenSecurityOriginalDTO.setPlatformId(platformId);
        tokenSecurityOriginalDTO.setAddress(tokenAd);
        tokenSecurityOriginalDTO.setSecurityLevel(-1);
        tokenSecurityOriginalDTO.setDataTimestamp(System.currentTimeMillis());
        return tokenSecurityOriginalDTO;
    }

    private TokenSecurityResponseDTO buildTokenSecurityResponseDTO(PlatformDTO platformDTO, TokenSecurityOriginalDTO originalDTO) {
        List<TokenSecurityResponseDTO.SecurityItem> securityItems = Lists.newArrayList();
        if(CollectionUtils.isNotEmpty(originalDTO.getSecurityItems())){
            securityItems = originalDTO.getSecurityItems().stream()
                .filter(item -> CollectionUtils.isNotEmpty(item.getDetails()))
                .flatMap(item -> item.getDetails().stream()
                    .map(t -> TokenSecurityResponseDTO.SecurityItem.builder()
                        .code(t.getTitle())
                        .riskCode(convertRiskCode(t.getTitle()))
                        .isHit(t.getIsHit())
                        .order(t.getOrder())
                        .des(t.getDescription())
                        .groupId(item.getId())
                        .riskyLevel(SecurityRiskLevelEnum.getCodeByRiskyLevel(t.getRiskType(), t.getIsHit()))
                        .build())
                ).collect(Collectors.toList());
        }

        TokenSecurityResponseDTO responseDTO = new TokenSecurityResponseDTO();
        responseDTO.setPlatformId(originalDTO.getPlatformId());
        responseDTO.setPlatformName(platformDTO.getDn());
        responseDTO.setTokenContractAddress(originalDTO.getAddress());
        responseDTO.setExtra(originalDTO.getExtra());
        responseDTO.setSecurityLevel(SecurityRiskLevelEnum.getDisplay(originalDTO.getSecurityLevel()));
        responseDTO.setSecurityItems(securityItems);
        responseDTO.setEvmDisplay(originalDTO.getPlatformId() == SOLANA_PLATFORM_ID ? null : buildEvmDisplay(originalDTO));
        responseDTO.setSolanaDisplay(originalDTO.getPlatformId() == SOLANA_PLATFORM_ID ? buildSolanaDisplay(originalDTO) : null);

        return responseDTO;
    }

    private String convertRiskCode(String code){
        if(StringUtils.isBlank(code)){
            return null;
        }
        return binanceCodeRefMap.entrySet().stream()
            .filter(entrySet -> entrySet.getValue().contains(code.toLowerCase()))
            .findFirst()
            .map(Map.Entry::getKey)
            .orElse(null);
    }

    private Mono<List<TokenSecurityOriginalDTO>> putCache(Integer platformId, Collection<String> tokenAds){
        return fromDB(platformId, tokenAds)
            .filter(CollectionUtils::isNotEmpty)
            .doOnNext(resultList -> {
                Map<String, String> securityMap = resultList.stream()
                    .collect(Collectors.toMap(d -> String.format(TOKEN_SECURITY_SYNC_KEY, platformId.toString(),
                        d.getAddress()), d -> JacksonUtils.toJson(d), (o1, o2) -> o1));

                asyncExecuteWithSubscribe(() -> tokenSecurityRepository.setMultiValue(securityMap, securityCacheExpire));
            }).defaultIfEmpty(Collections.emptyList());
    }

    private Mono<List<TokenSecurityOriginalDTO>> fromDB(Integer platformId, Collection<String> tokenAds){
        if(CollectionUtils.isEmpty(tokenAds)){
            return Mono.just(Collections.emptyList());
        }
        return tokenSecurityMongoRepository.queryByAddressList(platformId, tokenAds)
            .flatMapMany(Flux::fromIterable)
            .filter(securityEntity -> securityEntity.getIsSupported() == null || Boolean.TRUE.equals(securityEntity.getIsSupported()))
            .map(tokenSecurityEntityToOriginalDtoConverter::convert)
            .collectList();
    }

    private Mono<List<TokenSecurityEntity>> updateTokenSecurity(Integer platformId, List<String> tokenAddressList, String businessType){
        businessType = StringUtils.isBlank(businessType) ? bscSecurityBusiness : businessType;
        return binanceClient.batchQueryTokenSecurity(platformId, tokenAddressList, businessType)
            .flatMap(securityResponse -> {
                List<TokenSecurityEntity> tokenSecurityEntities = securityResponse.getData().stream()
                    .map(securityDto -> {
                        TokenSecurityEntity tokenSecurityEntity = tokenSecurityParsedDtoToEntityConverter.convert(securityDto);
                        tokenSecurityEntity.setPlatformId(platformId);
                        return tokenSecurityEntity;
                    }).collect(Collectors.toList());

                return tokenSecurityMongoRepository.batchInsertOrUpdate(tokenSecurityEntities, true)
                    .map(r -> tokenSecurityEntities)
                    .doOnError(e -> log.error("Token security update error. tokenSecurityEntities: {}", JacksonUtils.toJson(tokenSecurityEntities), e));
            }).defaultIfEmpty(Collections.emptyList());
    }

    private Mono<Boolean> updateTokenDetailLevel(List<TokenSecurityEntity> tokenSecurityEntities){
        return Flux.fromIterable(tokenSecurityEntities)
            .map(entity -> DexTokenDetailEntity.builder()
                .platform(entity.getPlatformId())
                .address(entity.getAddress())
                .riskLevel(entity.getSecurityLevel())
                .build())
            .collectList()
            .publishOn(Schedulers.boundedElastic())
            .flatMap(entities -> tokenService.sendDexTokenDetailEvent(entities, EventTypeEnum.UPDATE))
            .thenReturn(true);
    }

    private Mono<Boolean> updateCache(List<TokenSecurityEntity> tokenSecurityEntities){
        if(CollectionUtils.isEmpty(tokenSecurityEntities)){
            return Mono.just(false);
        }
        Integer platformId = tokenSecurityEntities.get(0).getPlatformId();
        return Mono.justOrEmpty(tokenSecurityEntities)
            .map(tokenSecurityEntityToOriginalDtoConverter::convertList)
            .filter(CollectionUtils::isNotEmpty)
            .flatMap(resultList -> {
                Map<String, String> securityMap = resultList.stream()
                    .collect(Collectors.toMap(d -> String.format(TOKEN_SECURITY_SYNC_KEY, platformId.toString(),
                        d.getAddress()), d -> JacksonUtils.toJson(d), (o1, o2) -> o1));

                return tokenSecurityRepository.setMultiValue(securityMap, securityCacheExpire);
            })
            .doOnError(e -> log.error("Token security update cache error. tokenSecurityEntities {}", JacksonUtils.toJson(tokenSecurityEntities), e))
            .thenReturn(true);
    }

    private TokenSecurityResponseDTO.DisplayItem buildEvmDisplay(TokenSecurityOriginalDTO originalDTO){

        TokenSecurityResponseDTO.DisplayItem evmDisplayDTO = TokenSecurityResponseDTO.DisplayItem.builder()
            .honeypotStatus(DisplayRiskLevelStatusEnum.UNKNOWN.getValue())
            .unverifiedContractStatus(DisplayRiskLevelStatusEnum.UNKNOWN.getValue())
            .rugPullStatus(DisplayRiskLevelStatusEnum.UNKNOWN.getValue())
            .fakeTokenStatus(DisplayRiskLevelStatusEnum.UNKNOWN.getValue())
            .build();

        List<String> codes = getCodes(originalDTO);
        if(CollectionUtils.isEmpty(codes)){
            return evmDisplayDTO;
        }

        if(codes.contains(TokenSecurityConstants.HONEYPOT)){
            evmDisplayDTO.setHoneypotStatus(DisplayRiskLevelStatusEnum.YES.getValue());
        }

        if(codes.contains(TokenSecurityConstants.HONEYPOT_NOT_FOUND)){
            evmDisplayDTO.setHoneypotStatus(DisplayRiskLevelStatusEnum.NO.getValue());
        }

        if(codes.contains(TokenSecurityConstants.UNVERIFIED_CONTRACT)){
            evmDisplayDTO.setUnverifiedContractStatus(DisplayRiskLevelStatusEnum.YES.getValue());
        }

        if(codes.contains(TokenSecurityConstants.VERIFIED_CONTRACT)){
            evmDisplayDTO.setUnverifiedContractStatus(DisplayRiskLevelStatusEnum.NO.getValue());
        }

        if(codes.contains(TokenSecurityConstants.RUG_PULL)){
            evmDisplayDTO.setRugPullStatus(DisplayRiskLevelStatusEnum.YES.getValue());
        }

        if(codes.contains(TokenSecurityConstants.RUG_PULL_NOT_FOUND)){
            evmDisplayDTO.setRugPullStatus(DisplayRiskLevelStatusEnum.NO.getValue());
        }

        if(codes.contains(TokenSecurityConstants.FAKE_TOKEN)){
            evmDisplayDTO.setFakeTokenStatus(DisplayRiskLevelStatusEnum.YES.getValue());
        }

        if(codes.contains(TokenSecurityConstants.FAKE_TOKEN_NOT_FOUND)){
            evmDisplayDTO.setFakeTokenStatus(DisplayRiskLevelStatusEnum.NO.getValue());
        }
        return evmDisplayDTO;
    }

    private TokenSecurityResponseDTO.DisplayItem buildSolanaDisplay(TokenSecurityOriginalDTO originalDTO){

        TokenSecurityResponseDTO.DisplayItem solanaDisplayDTO = TokenSecurityResponseDTO.DisplayItem.builder()
            .mintableStatus(DisplayRiskLevelStatusEnum.UNKNOWN.getValue())
            .freezableStatus(DisplayRiskLevelStatusEnum.UNKNOWN.getValue())
            .rugPullStatus(DisplayRiskLevelStatusEnum.UNKNOWN.getValue())
            .fakeTokenStatus(DisplayRiskLevelStatusEnum.UNKNOWN.getValue())
            .build();

        List<String> codes = getCodes(originalDTO);
        if(CollectionUtils.isEmpty(codes)){
            return solanaDisplayDTO;
        }

        if(codes.contains(TokenSecurityConstants.MINTABLE)){
            solanaDisplayDTO.setMintableStatus(DisplayRiskLevelStatusEnum.YES.getValue());
        }

        if(codes.contains(TokenSecurityConstants.MINTING_RISK_NOT_FOUND)){
            solanaDisplayDTO.setMintableStatus(DisplayRiskLevelStatusEnum.NO.getValue());
        }

        if(codes.contains(TokenSecurityConstants.FREEZABLE)){
            solanaDisplayDTO.setFreezableStatus(DisplayRiskLevelStatusEnum.YES.getValue());
        }

        if(codes.contains(TokenSecurityConstants.FREEZABLE_NOT_FOUND)){
            solanaDisplayDTO.setFreezableStatus(DisplayRiskLevelStatusEnum.NO.getValue());
        }

        if(codes.contains(TokenSecurityConstants.RUG_PULL)){
            solanaDisplayDTO.setRugPullStatus(DisplayRiskLevelStatusEnum.YES.getValue());
        }

        if(codes.contains(TokenSecurityConstants.RUG_PULL_NOT_FOUND)){
            solanaDisplayDTO.setRugPullStatus(DisplayRiskLevelStatusEnum.NO.getValue());
        }

        if(codes.contains(TokenSecurityConstants.FAKE_TOKEN)){
            solanaDisplayDTO.setFakeTokenStatus(DisplayRiskLevelStatusEnum.YES.getValue());
        }

        if(codes.contains(TokenSecurityConstants.FAKE_TOKEN_NOT_FOUND)){
            solanaDisplayDTO.setFakeTokenStatus(DisplayRiskLevelStatusEnum.NO.getValue());
        }
        return solanaDisplayDTO;
    }

    private List<String> getCodes(TokenSecurityOriginalDTO originalDTO){
        if(CollectionUtils.isNotEmpty(originalDTO.getSecurityItems())){
            return originalDTO.getSecurityItems().stream()
                .filter(d -> CollectionUtils.isNotEmpty(d.getDetails()))
                .flatMap(d -> d.getDetails().stream().filter(t -> StringUtils.isNotBlank(t.getTitle())).map(t -> t.getTitle()))
                .toList();
        }

        return List.of();
    }
}
