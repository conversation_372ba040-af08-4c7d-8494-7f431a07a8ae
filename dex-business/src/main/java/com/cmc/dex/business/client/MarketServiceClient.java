package com.cmc.dex.business.client;

import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;
import com.cmc.dex.business.client.model.CryptoContractsDTO;
import com.cmc.dex.business.client.model.CryptoDTO;
import com.cmc.dex.business.client.model.ResponseDTO;
import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.framework.http.HttpWebClient;
import com.cmc.framework.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @created on 2025/03/17
 * @inside the package - com.cmc.transformers.integration.internal
 **/

@Slf4j
@Component
public class MarketServiceClient {

    @Autowired
    @Qualifier("defaultHttpClient")
    protected HttpWebClient client;

    @Value("${com.cmc.market-service.url:}")
    private String url;

    @Value("${com.cmc.cryptos-currency.url:https://api.coinmarketcap.com/data-api/v3/map/all?dataType=cryptoCurrency}")
    private String cryptosCurrencyUrl;

    /**
     * get all crypto contracts
     *
     * @return Mono<List<CryptoContractsDTO>>
     */
    public Mono<List<CryptoContractsDTO>> getAllCryptoContracts() {
        String targetUrl = url + "/contracts/cryptos";
        return client.get(targetUrl)
                .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
                .map(response -> JacksonUtils.deserialize(response.getBody(), new TypeReference<List<CryptoContractsDTO>>() {
                }))
                .doOnError(e -> log.error("getAllCryptoContracts error:", e));
    }

    public Mono<CryptoDTO> getCryptoCurrency() {
        return client.get(cryptosCurrencyUrl)
            .publishOn(DexSchedulers.business())
            .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
            .flatMap(response -> {
                CryptoDTO result =
                    JacksonUtils.deserialize(response.getBody(), new TypeReference<ResponseDTO<CryptoDTO>>() {
                    }).getData();
                return Mono.just(result);
            })
            .doOnError(e -> log.error("getCryptoCurrency error:", e))
            .onErrorResume(e -> Mono.empty());
    }

}
