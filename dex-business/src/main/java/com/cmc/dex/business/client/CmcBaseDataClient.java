package com.cmc.dex.business.client;

import com.cmc.dex.common.constants.MetricConstants;
import com.cmc.dex.model.token.CryptoCurrencyExchange;
import com.cmc.framework.http.HttpWebClient;
import com.cmc.framework.metrics.CmcMeterRegistry;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;
import reactor.util.retry.RetryBackoffSpec;
import org.apache.commons.collections.CollectionUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Component;
import org.springframework.web.reactive.function.client.WebClientResponseException;
import com.fasterxml.jackson.core.type.TypeReference;

import java.io.IOException;
import java.time.Duration;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @description query data from cmc website
 * @since 2025-05-10
 */
@Component
@Slf4j
public class CmcBaseDataClient {

    @Value("${cmc.base.data.get-crypto-id.api:https://api.beta.coinmarketcap.supply/base-data/system/v3/detail-other/get-crypto-id-by-contract-address?addresses=%s}")
    private String getCryptoIdApi;

    @Value("${cmc.base.data.get-crypto-currency-exchange.api:https://api.beta.coinmarketcap.supply/base-data/system/v3/crypto/currency/exchange?ids=%s}")
    private String getCryptoCurrencyExchangeApi;

    @Value("${cmc.base.data.api.retry.max-attempts:3}")
    private long retryMaxAttempts;

    @Value("${cmc.base.data.api.retry.min-backoff:200}")
    private long retryMinBackoff;

    @Resource
    @Qualifier("defaultHttpClient")
    protected HttpWebClient httpWebClient;

    @Resource
    private CmcMeterRegistry cmcMeterRegistry;

    public Mono<ResponseEntity<List<Integer>>> getCryptoIdsByAddresses(List<String> addresses) {
        if (CollectionUtils.isEmpty(addresses)) {
            return Mono.empty();
        }
        String url = String.format(getCryptoIdApi, StringUtils.join(addresses, ","));
        return doGet(url, "getCryptoIdsByAddresses", r -> {
            // convert to List<Integer>
            String[] ids = r.getBody().replaceAll("[\\[\\]]", "").split(",");
            List<Integer> idList = Arrays.stream(ids)
                .map(id -> StringUtils.isBlank(id) ? null : Integer.parseInt(id))
                .collect(Collectors.toList());
            return new ResponseEntity<>(idList, r.getStatusCode());
        });
    }

    public Mono<ResponseEntity<Map<String, List<CryptoCurrencyExchange>>>> getCryptoCurrencyExchangesMap(
        List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return Mono.empty();
        }
        String url = String.format(getCryptoCurrencyExchangeApi, StringUtils.join(ids, ","));
        return doGet(url, "getCryptoCurrencyExchangesMap", r -> {
            // convert to Map<String, List<CryptoCurrencyExchange>>
            Map<String, List<CryptoCurrencyExchange>> map =
                JacksonUtils.deserialize(r.getBody(), new TypeReference<Map<String, String>>() {
                    })
                    .entrySet()
                    .stream()
                    .collect(Collectors.toMap(Map.Entry::getKey,
                        entry -> {
                            List<CryptoCurrencyExchange> exchanges = JacksonUtils.deserialize(entry.getValue(), new TypeReference<>() {});
                            return exchanges.stream()
                                .filter(exchange -> exchange.getDexStatus() != null && exchange.getDexStatus() == 0)
                                .collect(Collectors.toList());
                        }));
            return new ResponseEntity<>(map, r.getStatusCode());
        });
    }

    public <T> Mono<ResponseEntity<T>> doGet(String url, String apiName,
        Function<ResponseEntity<String>, ResponseEntity<T>> convertFunction) {
        return httpWebClient.get(url)
            .doFirst(
                () -> cmcMeterRegistry.counter(MetricConstants.CMC_BASE_DATA_API_CALLS, MetricConstants.TAG_API_NAME, apiName))
            .doOnError(e -> {
                log.error("{} error", apiName, e);
                cmcMeterRegistry.counter(MetricConstants.CMC_BASE_DATA_API_ERRORS, MetricConstants.TAG_API_NAME, apiName);
            })
            .doOnSuccess(response -> {
                if (!response.getStatusCode().is2xxSuccessful()) {
                    cmcMeterRegistry.counter(MetricConstants.CMC_BASE_DATA_API_ERRORS, MetricConstants.TAG_API_NAME, apiName, MetricConstants.TAG_HTTP_STATUS, response.getStatusCode().value() + "");
                }
            })
            .retryWhen(getRetrySpec())
            .filter(r -> StringUtils.isNotBlank(r.getBody()))
            .map(convertFunction);
    }

    private @NotNull RetryBackoffSpec getRetrySpec() {
        return Retry.backoff(retryMaxAttempts, Duration.ofMillis(retryMinBackoff)).filter(e -> {
            if (e instanceof WebClientResponseException) {
                int statusCode = ((WebClientResponseException)e).getStatusCode().value();
                // 4xx or 5xx retry
                return statusCode >= 400 && statusCode < 600;
            }
            // IOException retry
            return e instanceof IOException;
        }).doAfterRetry(retrySignal -> log.error("Retry after error", retrySignal.failure()));
    }

}