package com.cmc.dex.business.client;

import com.cmc.dex.business.client.sign.SignUtil;
import com.cmc.dex.model.security.BnSecurityResponseDTO;
import com.cmc.dex.model.security.ChainDTO;
import com.cmc.dex.model.security.ParsedTokenSecurityDTO;
import com.cmc.framework.http.HttpWebClient;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import com.ctrip.framework.apollo.spring.annotation.ApolloJsonValue;
import com.fasterxml.jackson.core.type.TypeReference;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.stereotype.Component;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Flux;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/**
 * @Description
 * <AUTHOR> Z
 * @CreateTime 2025-05-21
 */
@Component
@Slf4j
public class BinanceClient {

    public static final String ITEMS_URL_SUFFIX = "%s/security-api/public/app/v1/detect?business=%s";
    public static final String BATCH_URL_SUFFIX = "%s/security-api/public/app/v1/batch/detect?business=%s";
    private static final String HEADER_LANG = "lang";
    private static final String HEADER_LANG_CODE = "en";

    @Value("${com.cmc.dexer.binance.security.url:https://cb.commonservice.io}")
    private String bscSecurityUrl;
    @Value("${com.cmc.dexer.binance.security.app-id:2b3531d7922c4659bd3d}")
    private String appId;
    @Value("${com.cmc.dexer.binance.security.app-secret:1fa62142ed6441978d7b93f2a94894a1}")
    private String appSecret;
    @Value("${com.cmc.dexer.binance.security.api.batch-size:100}")
    private Integer apiBatchSize;
    @ApolloJsonValue("${binance.cmc-binance.chain.map:}")
    private List<ChainDTO> chainRefDTOS;

    @Resource
    @Qualifier("defaultHttpClient")
    protected HttpWebClient httpWebClient;

    public Mono<BnSecurityResponseDTO<ParsedTokenSecurityDTO>> queryTokenSecurity(Integer platformId,
        String tokenAddress, String business) {

        if(platformId == null || !getChainRefMap().containsKey(platformId) ||
            StringUtils.isBlank(tokenAddress) || StringUtils.isBlank(business)){
            return Mono.empty();
        }
        Map<String, String> params = buildRequestMap(getChainRefMap().get(platformId).getBnChainId(), tokenAddress);
        String url = String.format(ITEMS_URL_SUFFIX, bscSecurityUrl, business);
        String headerApi = String.format(ITEMS_URL_SUFFIX, StringUtils.EMPTY, business);
        return httpWebClient.post(url, params, buildHttpHeaders(headerApi, params))
            .filter(responseEntity -> {
                if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                    return true;
                } else {
                    log.warn("[BN] Token security query items exception URL {}, result:{}", url, responseEntity);
                    return false;
                }
            })
            .map(response -> JacksonUtils.deserialize(response.getBody(), new TypeReference<BnSecurityResponseDTO<ParsedTokenSecurityDTO>>() {}))
            .doOnError(throwable -> log.error("[BN] Token security query items failed url {}", url, throwable))
            .retryWhen(Retry.backoff(2, Duration.of(200, ChronoUnit.MILLIS)));
    }

    public Mono<BnSecurityResponseDTO<List<ParsedTokenSecurityDTO>>> batchQueryTokenSecurity(Integer platformId,
        List<String> tokenAddressList, String business){

        if(platformId == null || !getChainRefMap().containsKey(platformId)
            || CollectionUtils.isEmpty(tokenAddressList) || StringUtils.isBlank(business)) {
            return Mono.empty();
        }
        return Flux.fromIterable(Lists.partition(tokenAddressList, apiBatchSize))
            .flatMap(subList -> {
                Map<String, Map<String, String>> paramMap = subList.stream()
                    .map(tokenAddress -> buildRequestMap(getChainRefMap().get(platformId).getBnChainId(), tokenAddress))
                    .collect(Collectors.toMap(m -> m.get("requestId"), Function.identity(), (o1, o2) -> o1));

                String url = String.format(BATCH_URL_SUFFIX, bscSecurityUrl, business);
                String headerApi = String.format(BATCH_URL_SUFFIX, StringUtils.EMPTY, business);
                return httpWebClient.post(url, paramMap.values(), buildHttpHeaders(headerApi, paramMap.values()))
                    .retryWhen(Retry.backoff(2, Duration.of(200, ChronoUnit.MILLIS)))
                    .filter(responseEntity -> {
                        log.debug("[BN] Batch detext api url {}, param {}, responseEntity {}",
                            url, JacksonUtils.toJson(paramMap.values()), JacksonUtils.toJson(responseEntity));
                        if (responseEntity.getStatusCode().is2xxSuccessful() && responseEntity.getBody() != null) {
                            return true;
                        } else {
                            log.warn("[BN] Token security batch query exception URL {}, result:{}", url, responseEntity);
                            return false;
                        }
                    })
                    .flatMapMany(response -> {
                        BnSecurityResponseDTO<List<ParsedTokenSecurityDTO>> responseDTO =
                            JacksonUtils.deserialize(response.getBody(), new TypeReference<>() {});
                        return Flux.fromIterable(responseDTO.getData());
                    })
                    .doOnError(throwable -> log.error("[BN] Token security batch query failed url {}, param {}",
                        url, JacksonUtils.toJson(paramMap.values()), throwable));
            }).collectList()
            .map(list -> {
                BnSecurityResponseDTO<List<ParsedTokenSecurityDTO>> responseDTO = new BnSecurityResponseDTO();
                responseDTO.setData(list);
                return responseDTO;
            });
    }

    private Map<Integer, ChainDTO> getChainRefMap(){
        if(CollectionUtils.isEmpty(chainRefDTOS)){
            return Maps.newHashMap();
        }

        return chainRefDTOS.stream()
            .collect(Collectors.toMap(ChainDTO::getCmcPlatformId, Function.identity(), (o1, o2) -> o1));
    }

    private Map<String, String> buildRequestMap(String bscChainId, String address) {
        Map<String, String> requestMap = new HashMap<>();
        requestMap.put("requestId", address);
        requestMap.put("chainId", bscChainId);
        requestMap.put("address", address);
        return requestMap;
    }

    private Consumer<HttpHeaders> buildHttpHeaders(String headerApi, Object body) {
        return httpHeaders -> {
            Map<String, String> params = SignUtil.buildPostHeader(appId, appSecret, headerApi, body);
            params.forEach(httpHeaders::add);
            httpHeaders.add(HEADER_LANG, HEADER_LANG_CODE);
        };
    }

}
