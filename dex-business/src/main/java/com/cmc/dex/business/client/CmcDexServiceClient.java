package com.cmc.dex.business.client;

import com.cmc.dex.business.client.model.PairInfoDTO;
import com.cmc.dex.business.client.model.PairInfoRequestDTO;
import com.cmc.dex.business.client.model.ResponseDTO;
import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.framework.http.HttpWebClient;
import com.cmc.framework.utils.CollectionUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.fasterxml.jackson.core.type.TypeReference;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/**
 * <AUTHOR>
 */
@Component
@Slf4j
public class CmcDexServiceClient {
    @Autowired
    @Qualifier("defaultHttpClient")
    protected HttpWebClient httpWebClient;
    @Value("${com.cmc.dex.pair-info.url:https://api.coinmarketcap.com/dexer/v3/dexer/pair-simple-info}")
    private String pairInfoUrl;

    public Mono<List<PairInfoDTO>> getPairInfo(List<PairInfoRequestDTO> pairInfoRequestDTOList) {
        return httpWebClient.post(pairInfoUrl, pairInfoRequestDTOList)
            .publishOn(DexSchedulers.business())
            .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
            .flatMap(response -> {
                List<PairInfoDTO> result =
                    JacksonUtils.deserialize(response.getBody(), new TypeReference<ResponseDTO<List<PairInfoDTO>>>() {
                    }).getData();
                if (CollectionUtils.isEmpty(result)) {
                    return Mono.empty();
                } else {
                    return Mono.just(result);
                }
            })
            .doOnError(e -> log.error("getPairInfo error:", e))
            .onErrorResume(e -> Mono.empty());
    }
}
