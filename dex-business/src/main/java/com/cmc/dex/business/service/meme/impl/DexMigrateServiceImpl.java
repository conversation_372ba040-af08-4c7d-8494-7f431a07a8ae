package com.cmc.dex.business.service.meme.impl;

import com.cmc.dex.business.service.meme.DexMigrateService;
import com.cmc.dex.dao.entity.tidb.DexTokenDetailEntity;
import com.cmc.dex.dao.repository.redis.impl.DexMigrateRedisRepositoryImpl;
import com.cmc.dex.dao.repository.tidb.repository.DexMigrateDetailRepository;
import com.cmc.dex.dao.repository.tidb.repository.DexTokenDetailRepository;
import com.cmc.dex.model.token.MigrateCountDTO;
import com.cmc.framework.utils.DatetimeUtils;
import com.cmc.framework.utils.JacksonUtils;
import com.cmc.framework.utils.StringUtils;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import jakarta.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

/**
 * DevMigrateServiceImpl
 * <AUTHOR> ricky.x
 * @date: 2025/6/23 15:32
 */
@Slf4j
@Service
public class DexMigrateServiceImpl implements DexMigrateService {

    @Resource
    private DexMigrateDetailRepository dexMigrateDetailRepository;

    @Resource
    private DexTokenDetailRepository dexTokenDetailRepository;

    @Resource
    private DexMigrateRedisRepositoryImpl dexMigrateRedisRepository;

    @Value("${com.cmc.dquery-service.meme-user-token.days-ago:7}")
    private Integer daysAgo;

    @Value("${com.cmc.dquery-service.meme-user-token.query-limit:500}")
    private Integer queryLimit;


    @Override
    public Mono<MigrateCountDTO> getMigrateCount(Integer platformId, String userAddress) {
        MigrateCountDTO defaultResult = MigrateCountDTO.builder().build();
        if (platformId == null || StringUtils.isBlank(userAddress)) {
            return Mono.just(defaultResult);
        }
        return getMigrateCountFormCache(platformId, userAddress)
                .flatMap(count -> {
                    defaultResult.setPlatformId(platformId);
                    defaultResult.setCreator(userAddress);
                    defaultResult.setCount(count);
                    if (count != null && count > 0) {
                        return userLatestTokenCa(platformId, userAddress)
                                .map(latestTokens -> {
                                    defaultResult.setLatestTokens(latestTokens);
                                    return defaultResult;
                                });
                    }
                    return Mono.just(defaultResult);
                })
                .defaultIfEmpty(defaultResult);
    }

    private Mono<List<String>> userLatestTokenCa(Integer platformId, String userAddress) {
        Date gtePublishAt = DatetimeUtils.getDaysAgo(new Date(), daysAgo);
        Integer limit = (queryLimit == null || queryLimit <= 0) ? null : queryLimit;
        return dexTokenDetailRepository.batchGetUserToken(platformId, userAddress, gtePublishAt, limit)
                .map(e -> e.stream().map(DexTokenDetailEntity::getAddress).collect(Collectors.toList()))
                .defaultIfEmpty(List.of());
    }


    private Mono<Integer> getMigrateCountFormCache(Integer platformId, String userAddress) {
        return dexMigrateRedisRepository.getMigrateCount(platformId, userAddress)
                .filter(StringUtils::isNotBlank)
                .map(str -> JacksonUtils.deserialize(str, Integer.class))
                .switchIfEmpty(Mono.defer(() -> getMigrateCountFormDb(platformId, userAddress)
                        .publishOn(Schedulers.boundedElastic())
                        .doOnNext(data -> dexMigrateRedisRepository.setMigrateCount(platformId, userAddress, data)
                                .subscribe())));
    }


    private Mono<Integer> getMigrateCountFormDb(Integer platformId, String userAddress) {
        return dexMigrateDetailRepository.countByPlatformAndUserCa(platformId, userAddress);
    }
}