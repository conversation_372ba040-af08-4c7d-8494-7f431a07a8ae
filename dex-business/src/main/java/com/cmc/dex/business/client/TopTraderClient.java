package com.cmc.dex.business.client;

import com.cmc.dex.model.profit.ProfitTopTraderRespDTO;
import com.cmc.framework.http.HttpWebClient;
import com.cmc.framework.utils.JacksonUtils;
import java.time.Duration;
import java.time.temporal.ChronoUnit;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import lombok.extern.slf4j.Slf4j;
import reactor.core.publisher.Mono;
import reactor.util.retry.Retry;

/**
 * TopTraderClient
 * <AUTHOR> ricky.x
 * @date: 2025/5/21 16:06
 */
@Component
@Slf4j
public class TopTraderClient {

    @Autowired
    @Qualifier("defaultHttpClient")
    protected HttpWebClient client;

    @Value("${com.cmc.dquery-prod-service.url:https://dapi.coinmarketcap.com}")
    private String url;


    private final static String TOP_TRADER_URL = "/dex/v1/profit/top-trader?period=%s&platform=%s";
    public Mono<ProfitTopTraderRespDTO> getTopTrader(String interval, String platform) {
        String targetUrl = url + String.format(TOP_TRADER_URL, interval, platform);
        return client.get(targetUrl)
                .retryWhen(Retry.backoff(2, Duration.of(300, ChronoUnit.MILLIS)))
                .map(response -> JacksonUtils.deserialize(response.getBody(), ProfitTopTraderRespDTO.class))
                .doOnError(e -> log.error("getTopTrader failed, params:{}, error:", targetUrl, e));
    }
}
