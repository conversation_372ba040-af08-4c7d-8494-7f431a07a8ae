package com.cmc.dex.business.client;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyCollection;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;
import static org.mockito.MockitoAnnotations.openMocks;
import static org.testng.Assert.assertEquals;
import static org.testng.Assert.assertNotNull;
import static org.testng.Assert.assertTrue;

import com.cmc.dex.business.client.model.PairInfoDTO;
import com.cmc.dex.business.client.model.PairInfoRequestDTO;
import com.cmc.dex.common.util.DexSchedulers;
import com.cmc.framework.http.HttpWebClient;
import com.cmc.framework.utils.CollectionUtils;
import java.util.List;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.springframework.http.ResponseEntity;
import org.springframework.test.util.ReflectionTestUtils;
import org.testng.annotations.AfterMethod;
import org.testng.annotations.BeforeMethod;
import org.testng.annotations.Test;
import reactor.core.publisher.Mono;
import reactor.core.scheduler.Schedulers;

public class CmcDexServiceClientTest {
    @InjectMocks
    private CmcDexServiceClient cmcDexServiceClient;
    @Mock
    private HttpWebClient httpWebClient;

    private AutoCloseable mockitoCloseable;

    @BeforeMethod
    public void setUp() {
        mockitoCloseable = openMocks(this);
        ReflectionTestUtils.setField(cmcDexServiceClient, "pairInfoUrl", "www");
    }

    @AfterMethod
    public void tearDown() throws Exception {
        mockitoCloseable.close();
    }

    @Test
    public void testGetPairInfo() {
        MockedStatic<DexSchedulers> dexSchedulersMockedStatic = mockStatic(DexSchedulers.class);
        when(DexSchedulers.business()).thenReturn(Schedulers.boundedElastic());
        String data =
            "{\"data\":[{\"poolId\":\"8945317\",\"platformId\":\"1\",\"pairContractAddress\":\"******************************************\"}],\"status\":{\"timestamp\":\"2025-05-21T07:53:59.694Z\",\"error_code\":\"0\",\"error_message\":\"SUCCESS\",\"elapsed\":\"0\",\"credit_count\":0}}";

        List<PairInfoRequestDTO> requestDTOList = List.of(PairInfoRequestDTO.builder()
            .platformId(1)
            .pairContractAddress("******************************************")
            .build());

        when(httpWebClient.post(anyString(), anyCollection())).thenReturn(Mono.just(ResponseEntity.ok(data)));

        List<PairInfoDTO> result = cmcDexServiceClient.getPairInfo(requestDTOList).block();

        assertNotNull(result);
        assertEquals((long)result.get(0).getPoolId(), 8945317L);
        assertEquals(result.get(0).getPairContractAddress(), "******************************************");
        assertEquals((int)result.get(0).getPlatformId(), 1);

        dexSchedulersMockedStatic.close();
    }

    @Test
    public void testGetPairInfo_EmptyResponse() {
        MockedStatic<DexSchedulers> dexSchedulersMockedStatic = mockStatic(DexSchedulers.class);
        when(DexSchedulers.business()).thenReturn(Schedulers.boundedElastic());
        String data =
            "{\"status\":{\"timestamp\":\"2025-05-21T07:53:59.694Z\",\"error_code\":\"0\",\"error_message\":\"SUCCESS\",\"elapsed\":\"0\",\"credit_count\":0}}";

        List<PairInfoRequestDTO> requestDTOList = List.of(PairInfoRequestDTO.builder()
            .platformId(1)
            .pairContractAddress("******************************************")
            .build());

        when(httpWebClient.post(anyString(), anyCollection())).thenReturn(Mono.just(ResponseEntity.ok(data)));

        List<PairInfoDTO> result = cmcDexServiceClient.getPairInfo(requestDTOList).block();

        assertTrue(CollectionUtils.isEmpty(result));

        dexSchedulersMockedStatic.close();
    }

}